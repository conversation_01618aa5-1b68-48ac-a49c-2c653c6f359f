import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from 'recharts';

interface ChallengeTrackerProps {
  data: Array<{
    day: string;
    completed: number;
    total: number;
  }>;
}

export function ChallengeTracker({ data }: ChallengeTrackerProps) {
  return (
    <div className="bg-white p-4 md:p-5 border border-border rounded-lg">
      <h2 className="text-paragraphContent text-body-lg font-bold mb-2">My Daily Challenge Tracker</h2>
      <div className="h-[250px] md:h-[300px] lg:h-[350px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 20, right: 0, left: -20, bottom: 20 }}
            barSize={50}
            barGap={0}
            barCategoryGap={10}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" vertical={true} horizontal={true} />
            <XAxis
              dataKey="day"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6B7280' }}
              dy={10}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              domain={[0, 8]}
              ticks={[0, 2, 4, 6, 8]}
              tick={{ fontSize: 12, fill: '#6B7280' }}
            />
            <Tooltip
              cursor={false}
              contentStyle={{ borderRadius: '8px', border: 'none', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}
              formatter={(value, name) => [
                name === 'completed' ? `${value} Completed` : `${value} Total`,
                name === 'completed' ? 'Completed Challenges' : 'Total Challenges'
              ]}
            />
            <Bar
              dataKey="total"
              stackId="a"
              fill="#47AEA9"
              radius={[0, 0, 0, 0]}
            />
            <Bar
              dataKey="completed"
              stackId="a"
              fill="#47AEA9CC"
              radius={[0, 0, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
      <div className="flex justify-center mt-4 space-x-6">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-[#4FD1C5] mr-2 rounded-sm"></div>
          <span className="text-xs text-gray-600">Completed Challenges</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-[#B2F5EA] mr-2 rounded-sm"></div>
          <span className="text-xs text-gray-600">Total Challenges</span>
        </div>
      </div>
    </div>
  );
} 