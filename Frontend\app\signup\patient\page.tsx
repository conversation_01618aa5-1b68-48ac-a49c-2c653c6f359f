"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { useRouter } from "next/navigation";
import { Eye, EyeOff } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function PatientSignupPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false,
    signUpForUpdates: true
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  }

  const handleSubmit = (e: React.FormEvent) => {
    setIsLoading(true);
    e.preventDefault();
    // Handle form submission logic here
    router.push("/verify-account");
  };

  return (
    <div className="flex flex-col">
      <main className="flex max-md:flex-col container-fluid lg:h-[calc(100vh-81px)]">
        {/* Left side - Image */}
        <div className="max-md:h-[450px] md:w-1/2 w-full relative">
          <Image
            src="/auth/signup.svg"
            alt="People sharing a meal"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - Provider Sign Up Form */}
        <div className="w-full md:w-1/2 flex items-center justify-end">
          <div className="w-full max-w-[650px] xl:pr-20 max-xl:px-5">
            <div>
              <div className="max-md:text-center text-paragraphContent">
                <p className="text-display-md mb-1 max-md:text-3xl max-md:mt-5">Hi Sarah,</p>
                <p className="text-display-md mb-1 max-md:text-3xl max-md:mt-5">Create an account to get started on your journey:</p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4 mt-10">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* User Name and Password */}
                  <div>
                    <label htmlFor="username" className="text-body-medium text-paragraphContent mb-1">
                      Username
                    </label>
                    <div className="flex items-center bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md">
                      <span className="text-gray-500 pl-3">@</span>
                      <Input
                        type="text"
                        id="username"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        disabled={isLoading}
                        className="w-full p-3 bg-landingBackground rounded-md border-none"
                        required
                      />
                    </div>
                    <p className="text-body-small text-coolGray60 mt-1">This will be public. You can change it anytime.</p>
                  </div>

                  {/* Password */}
                  <div>
                    <label htmlFor="password" className="text-body-medium text-paragraphContent mb-1">
                      Password
                    </label>
                    <div className="relative">
                      <Input
                        type={showPassword ? "text" : "password"}
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        className="p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md pr-10"
                        required
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-5 w-5 text-gray-400" />
                        ) : (
                          <Eye className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    <p className="text-body-small text-coolGray60 mt-1">It must be a combination of minimum 8 letters, numbers, and symbols.</p>
                  </div>
                </div>

                {/* Checkboxes */}
                <div className="space-y-3 mt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="rememberMe"
                      checked={formData.rememberMe}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, rememberMe: checked === true })
                      }
                      className="border-paragraphContent"
                    />
                    <label
                      htmlFor="rememberMe"
                      className="text-body-medium text-paragraphContent"
                    >
                      Remember me
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="signUpForUpdates"
                      checked={formData.signUpForUpdates}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, signUpForUpdates: checked === true })
                      }
                      className="border-paragraphContent"
                    />
                    <label
                      htmlFor="signUpForUpdates"
                      className="text-body-medium text-paragraphContent"
                    >
                      Sign me up for important news and updates
                    </label>
                  </div>
                </div>
                <Button
                  type="submit"
                  variant="netural"
                  className="w-full font-normal h-11 rounded-lg"
                  disabled={isLoading}
                >
                  <span className="text-white text-title-medium">{isLoading ? "Registering..." : "Sign Up"}</span>
                </Button>
              </form>
            </div>

            {/* Login link */}
            <div className="text-sm mt-8 pt-8 border-t border-gray-200 mb-5">
              Already have an account? <Link href="/login" className="text-black font-medium underline">Back to Log In</Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 