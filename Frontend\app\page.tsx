"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { WheelOfLifeSection } from "@/components/wheel-of-life-section";
import { useWindowSafe } from "@/hooks/use-client-only";
import { PromoSlider } from "@/components/PromoSlider";
import dynamic from "next/dynamic";
import { ArrowRightIcon } from "@/utils/icons";
import { useState, useEffect } from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const FeatureCards = dynamic(
  () => import("@/components/feature-cards").then((mod) => mod.FeatureCards),
  { ssr: false }
);

export default function Home() {
  const windowSafe = useWindowSafe();
  const [isVideoLoading, setIsVideoLoading] = useState(true);

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative md:py-24 min-h-[630px] bg-landingBackground my-auto flex items-between overflow-y-hidden overflow-x-hidden">
          <div className="relative container flex justify-between max-md:flex-col max-md:text-center max-md:px-4">
            <div className="space-y-6 md:max-w-[542px]">
              <h1 className="text-display-xl max-md:mt-6 pt-3 max-md:text-display-medium max-md:font-bold font-bold text-[#0D0D25]">
                Transform your life with Peakality.
              </h1>
              <p className="text-headline-small max-md:text-title-large pt-3 font-medium text-[#252837]">
                Embark on a transformational journey with our science-backed,
                expert-guided lifestyle challenges and content in mindfulness,
                personal growth, fitness, health, and beyond.
              </p>
              <div className="pt-3">
                <Link href="/signup">
                  <Button
                    variant="primary"
                    className="text-lightBlue px-6 py-7 flex items-center gap-5 tracking-wide max-md:mx-auto max-md:mb-3"
                  >
                    <span className="text-title-md">Sign up for FREE</span>
                    <ArrowRightIcon />
                  </Button>
                </Link>
              </div>
            </div>

            {isVideoLoading ? (
              <>
                <Skeleton
                  width={690}
                  height={400}
                  className="max-md:w-[320px] max-md:h-[180px]"
                />
              </>
            ) : (
              <div className="rounded-xl overflow-hidden shadow-xl border-3 max-w-[690px] py-4 px-4 max-h-[400px] max-md:my-4 border-white bg-white">
                <div className="relative aspect-video rounded overflow-hidden">
                  <video
                    className="w-full h-full object-cover"
                    controls
                    onLoadedData={() => setIsVideoLoading(false)}
                  >
                    <source src="/landing/video.mp4" type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Results Section */}
        <section className="bg-lightBlue text-paragraphContent py-10 md:py-12 px-20 max-md:px-4">
          <div className="text-center container max-md:px-4">
            <h2 className="text-display-lg max-md:text-4xl font-bold mb-6">
              Get results that last a lifetime.
            </h2>
            <p className="w-full text-center mb-6 text-title-medium">
              Setting goals is one thing, but making it easy to do in an already
              busy lifestyle is what we've done best to your reality and
              personality aim at peak performance.
            </p>
            <div>
              <FeatureCards />
            </div>
          </div>
        </section>

        <WheelOfLifeSection />
        {/* Promo Slider */}
        <PromoSlider />
      </main>
    </div>
  );
}
