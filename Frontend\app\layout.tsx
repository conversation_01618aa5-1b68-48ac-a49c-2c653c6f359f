import type { Metadata } from 'next';
import './globals.css';
import ClientHeader from './layout/client-header-wrapper';
import ClientFooter from './layout/client-footer-wrapper';
import { Roboto, Inter } from 'next/font/google'
import { Providers } from './providers';

const inter = Inter({ subsets: ['latin'], weight: ['400', '500', '700'], variable: '--font-inter' })
const roboto = Roboto({ subsets: ['latin'], weight: ['400', '500', '700'], variable: '--font-roboto' })

export const metadata: Metadata = {
  title: 'peakality',
  description: 'Created peakality',
  generator: 'peakality',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} ${roboto.variable}`}>
        <Providers>
          <ClientHeader />
          {children}
          <ClientFooter />
        </Providers>
      </body>
    </html>
  )
}
