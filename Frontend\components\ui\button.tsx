import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrapd font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        primary: "bg-primary text-lightBlue hover:bg-primary-hover focus:bg-primary-active active:bg-primary-active disabled:bg-primary-disabled",
        secondary: "bg-secondary text-white hover:bg-secondary-hover focus:bg-secondary-active active:bg-secondary-active disabled:bg-secondary-disabled",
        netural: "bg-darkBlueNormal text-white hover:bg-darkBlueNormalHover active:bg-darkBlueNormalActive disabled:bg-secondary-disabled",
        filledDark: "bg-neturalDark text-white hover:bg-neturalDark-hover focus:bg-neturalDark-active active:bg-neturalDark-active disabled:bg-neturalDark-disabled",
        filledDisabled: "bg-[#E0E3E7] text-[#A0A4A8] cursor-not-allowed",
        outlined: "border border-primary text-primary bg-transparent hover:border-primary",
        outlinedNeutral: "border border-neutral text-neutral bg-transparent hover:border-neutral",
        outlinedDark: "border border-darkBlueNormal text-[darkBlueNormal] bg-transparent hover:border-darkBlueNormalHover active:border-darkBlueNormalActive",
        outlinedDisabled: "border border-[#E0E3E7] text-[#A0A4A8] bg-transparent cursor-not-allowed",
        text: "bg-transparent text-[#546881] hover:bg-[#E0E3E7]",
        textSecondary: "bg-transparent text-[#A0A4A8] hover:bg-[#E0E3E7]",
        textDisabled: "bg-transparent text-[#E0E3E7] cursor-not-allowed",
        elevated: "bg-white text-[#546881] shadow-md hover:bg-[#E0E3E7]",
        elevatedSecondary: "bg-[#E0E3E7] text-[#546881] shadow-md hover:bg-[#D1D5DB]",
        elevatedDisabled: "bg-[#fdf8ea] text-[#A0A4A8] shadow-none cursor-not-allowed",
        tonal: "bg-[#fdf8ea] text-[#21272A] hover:bg-[#E0E3E7]",
        tonalSecondary: "bg-[#E0E3E7] text-[#546881] hover:bg-[#D1D5DB]",
        tonalDisabled: "bg-[#fdf8ea] text-[#A0A4A8] cursor-not-allowed",
      },
      size: {
        default: "h-12 text-body-medium px-6 rounded-lg",
        sm: "h-10 text-title-small px-6 rounded-lg",
      },
      withIcon: {
        true: "gap-4",
        false: "",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
      withIcon: false,
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, leftIcon, rightIcon, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    const withIcon = !!leftIcon || !!rightIcon
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, withIcon, className }))}
        ref={ref}
        {...props}
      >
        {leftIcon && <span className="inline-flex items-center">{leftIcon}</span>}
        {children}
        {rightIcon && <span className="inline-flex items-center">{rightIcon}</span>}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
