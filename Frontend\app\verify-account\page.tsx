"use client";

import Link from "next/link";
import Image from "next/image";
export default function VerifyAccountPage() {

  return (
    <div className="flex flex-col">
      <main className="flex max-md:flex-col container-fluid md:h-[calc(100vh-81px)]">
        {/* Left side - Image */}
        <div className="max-md:h-[450px] md:w-1/2 relative">
          <Image
            src="/auth/verify-account.svg"
            alt="People working together on a laptop"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - Verify Account Content */}
        <div className="w-full md:w-1/2 flex items-center justify-center">
          <div className="w-full max-w-[640px] px-8 py-12 max-md:text-center">
            <div>
              <h1 className="text-display-md text-paragraphContent max-md:text-3xl mb-2">Verify your account</h1>
              <p className="text-gray-600 mb-6 text-body-lg">
                For security reasons, we sent you an email or text message with a link
                to verify your account. Verify within 24 days of first signing up to avoid
                the deactivation of your account.
              </p>

              <p className="text-gray-600 text-body-lg mt-6 max-w-[500px]">
                If it doesn't arrive after a few minutes, check your spam folder,
                or <Link href="/login" className="underline">Click to Resend</Link>.
              </p>

              <div className="mt-10 pt-10 border-t border-gray-200">
                <p className="text-sm text-darkGray">
                  Back to <Link href="/login" className="underline">Log In</Link>
                </p>
              </div>

              <div className="mt-2">
                <p className="text-sm ">
                  No account yet? <Link href="/signup" className="underline">Sign Up</Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
