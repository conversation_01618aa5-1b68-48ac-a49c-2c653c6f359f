"use client"

import { Input } from "@/components/ui/input";
import DatePicker from "@/components/ui/DatePicker";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import dayjs from "dayjs";

export default function PatientDetailsForm({
  form,
  setForm,
  errors,
  dobValue,
  setDobValue,
  onNext,
}: any) {
  return (
    <form
      onSubmit={e => {
        e.preventDefault();
        onNext();
      }}
      className="max-w-[401px] mx-auto py-16"
    >
      <div className="mb-4">
        <p className="text-paragraphContent text-body-lg font-bold mb-3">
          Fill out patient details:
        </p>
        <hr className="mb-3 border-border" />
      </div>
      <div className="mb-8">
        <label className="block mb-1 text-body-medium text-paragraphContent font-bold">
          Patient Name
        </label>
        <Input
          className={`w-full mb-1 bg-landingBackground ${errors.name ? "mb-1 border-b-red-500" : ""}`}
          name="name"
          value={form.name}
          onChange={e => setForm((f: any) => ({ ...f, name: e.target.value }))}
          placeholder=""
          aria-invalid={!!errors.name}
          aria-describedby={errors.name ? "patient-name-error" : undefined}
        />
        {errors.name && (
          <div id="patient-name-error" className="text-red-500 text-sm mb-2">{errors.name}</div>
        )}
        <div className="text-body-small text-coolGrey60 mb-3">
          We won't show your full name publicly.
        </div>
      </div>
      <div className="mb-8">
        <label className="block mb-1 text-body-medium text-paragraphContent font-bold">
          Patient Date of Birth
        </label>
        <DatePicker
          value={dobValue}
          onChange={newValue => {
            setDobValue(newValue);
            setForm((f: any) => ({
              ...f,
              dob: newValue ? (dayjs.isDayjs(newValue) ? newValue.format("YYYY-MM-DD") : newValue) : "",
            }));
          }}
          error={!!errors.dob}
          helperText={errors.dob}
          aria-invalid={!!errors.dob}
          aria-describedby={errors.dob ? "patient-dob-error" : undefined}
        />
        {errors.dob && (
          <div id="patient-dob-error" className="text-red-500 text-sm mt-1">{errors.dob}</div>
        )}
      </div>
      <div className="mb-8">
        <label className="block mb-1 text-body-medium text-paragraphContent font-bold">
          Is the patient a military vet?
        </label>
        <div className="relative mb-1">
          <Select
            value={form.military || "unset"}
            onValueChange={(val: string) =>
              setForm((f: any) => ({ ...f, military: val === "unset" ? "" : val }))
            }
            aria-invalid={!!errors.military}
          >
            <SelectTrigger
              className={`w-full bg-landingBackground ${errors.military ? "border-red-400" : ""}`}
              aria-invalid={!!errors.military}
              aria-describedby={errors.military ? "patient-military-error" : undefined}
            >
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent className="bg-landingBackground">
              <SelectItem value="unset" disabled>
                Select an option
              </SelectItem>
              <SelectItem value="yes">Yes</SelectItem>
              <SelectItem value="no">No</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {errors.military && (
          <div id="patient-military-error" className="text-red-500 text-sm mb-2">{errors.military}</div>
        )}
      </div>
      <div className="flex justify-end">
        <Button variant="netural" size="sm" className="text-body-medium h-8">
          <span className="text-white">Next</span>
        </Button>
      </div>
    </form>
  );
}