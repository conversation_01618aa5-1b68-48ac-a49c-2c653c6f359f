"use client";

import React from "react";

export function SummaryPatientDetail() {

  function SummaryCard({ title, value, date }: { title: string; value: React.ReactNode; date: string }) {
    return (
      <div className="bg-white flex-1 border border-gray-200 h-20 rounded px-4 flex justify-center min-w-[180px] flex-col max-md:py-2">
        <span className="text-body-large text-paragraphContent text-coolGrey60">{title}</span>
        <div className="flex justify-between text-coolGrey900 w-full items-end">
          <span className="font-bold text-headline-small ">{value}</span>
          <span className="bg-gray-100 text-body-medium rounded-full px-3 h-6 flex items-center">{date}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col flex-wrap md:flex-row gap-4 mb-4">
      <SummaryCard title="Recovery Scale" value="180/230" date="Today" />
      <SummaryCard title="High-Risk Screening" value="High" date="Yesterday" />
      <SummaryCard title="Psychiatric Evaluation" value="80%" date="6/21/24" />
      <SummaryCard title="Pain Screening" value="8/10" date="Today" />
      <SummaryCard title="Depression Screening" value="Severe" date="Yesterday" />
    </div>
  );

}