import Image from "next/image";
import { CopyCard, ViewEyeBtn } from "@/utils/icons";

interface ProfileBillingCardProps {
  card: {
    name: string;
    number: string;
    expiry: string;
    cvv: string;
  };
}

export function ProfileBillingCard({ card }: ProfileBillingCardProps) {
  return (
    <div className="rounded w-[504px] max-md:w-full">
      <div>
        <div className="flex border bg-lightBlue px-5 py-3 justify-between mb-6 gap-2">
          <div className="text-body-large text-netural100 font-bold">
            Primary Card
          </div>
          <button className="text-gray-500 hover:text-gray-900 flex items-center gap-1 text-sm">
            <span>›</span>
          </button>
        </div>
        <div className="border rounded-lg p-5 bg-netural20">
          <div className="flex justify-center">
            <Image
              src="/dashboard/card.png"
              alt="Card"
              width={382}
              height={180}
              priority
              className="rounded-xl object-cover"
            />
          </div>
          <div className="mt-2 px-10 pr-16 max-md:px-2">
            <div className="mb-2 text-netural100 text-body-lg">
              Card Information
            </div>
            <div className="text-body-large text-netural90">
              <div className="flex justify-between mb-2">
                <span>Card No.</span>
                <div className="flex items-center gap-2 justify-end">
                  <button
                    type="button"
                    tabIndex={-1}
                    aria-label="Copy card number"
                  >
                    <CopyCard />
                  </button>
                  <span className="tracking-wider text-netural90">
                    {card.number}
                  </span>
                </div>
              </div>
              <div className="flex justify-between mb-2 text-netural90">
                <span>Expiry date</span>
                <div className="flex items-center gap-2 justify-end">
                  <span>{card.expiry}</span>
                </div>
              </div>
              <div className="flex justify-between mb-2 text-netural90">
                <span>CVV (3-digit security code)</span>
                <div className="flex items-center gap-2 justify-end">
                  <ViewEyeBtn />
                  <span>{card.cvv}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}