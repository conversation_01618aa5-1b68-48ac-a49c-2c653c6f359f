import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type UserRole = 'patient' | 'provider' | null;

export interface UserData {
  role: UserRole;
  username?: string;
  email?: string;
  // Add more fields as needed
}

interface UserState {
  user: UserData | null;
}

const initialState: UserState = {
  user: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state: UserState, action: PayloadAction<UserData | null>) => {
      state.user = action.payload;
    },
  },
});

export const { setUser } = userSlice.actions;
export default userSlice.reducer; 