import React from 'react';
import { useRouter } from 'next/navigation';
import { Trophy, Upload, ChevronLeft, ChevronRight } from 'lucide-react';

// Mock data for the Hero's Journey - matching your design exactly
const journeyLevels = [
  { area: 'Finance', level: 6, bgColor: 'bg-green-500', textColor: 'text-white' },
  { area: 'Romance', level: 4, bgColor: 'bg-pink-500', textColor: 'text-white' },
  { area: 'Family', level: 5, bgColor: 'bg-cyan-500', textColor: 'text-white' },
  { area: 'Creative', level: 4, bgColor: 'bg-amber-500', textColor: 'text-white' },
  { area: 'Physical', level: 2, bgColor: 'bg-lime-500', textColor: 'text-white' },
  { area: 'Social', level: 4, bgColor: 'bg-red-500', textColor: 'text-white' },
  { area: 'Career', level: 7, bgColor: 'bg-blue-600', textColor: 'text-white' },
  { area: 'Emotions', level: 5, bgColor: 'bg-orange-500', textColor: 'text-white' },
  { area: 'Spiritual', level: 4, bgColor: 'bg-purple-500', textColor: 'text-white' },
];

const badges = [
  {
    title: 'Champion',
    subtitle: 'Complete 5 challenges',
    icon: '🏆',
    color: '#FFD700',
    progress: '3/5'
  },
  {
    title: 'Champion',
    subtitle: 'Complete 5 challenges',
    icon: '🏆',
    color: '#FFD700',
    progress: '5/5'
  },
  {
    title: 'Champion',
    subtitle: 'Complete 5 challenges',
    icon: '🏆',
    color: '#FFD700',
    progress: '2/5'
  },
  {
    title: 'Adventurer',
    subtitle: 'Complete 3 challenges',
    icon: '🎯',
    color: '#9333EA',
    progress: '3/3'
  }
];

const activePrograms = [
  {
    title: 'Welcome to Peakality',
    image: '/dashboard/driver.svg',
    progress: 60,
    challenges: 3,
    duration: '30 days',
    reward: '900 pts'
  }
];

const activeChallenges = [
  {
    title: 'Wheel of Life Challenge',
    image: '/dashboard/driver.svg',
    progress: 75,
    duration: '14 days',
    reward: '500 pts'
  }
];

export function HeroJourney() {
  const router = useRouter();

  const handleUploadImage = () => {
    router.push('/dashboard/hero-journey/upload');
  };

  return (
    <div className="w-full space-y-4 sm:space-y-6 p-2 sm:p-4 lg:p-6">
      {/* Journey Levels - Horizontal Layout */}
      <div className="overflow-x-auto">
        <div className="flex gap-2 sm:gap-3 lg:gap-4 min-w-max pb-2">
          {journeyLevels.map((item, index) => (
            <div key={index} className="flex flex-col items-center min-w-[80px] sm:min-w-[90px] lg:min-w-[100px]">
              <div className="text-xs sm:text-sm lg:text-base text-gray-700 mb-1 sm:mb-2 font-medium text-center">
                {item.area}
              </div>
              <div
                className={`${item.bgColor} ${item.textColor} px-2 py-1 sm:px-3 sm:py-2 lg:px-4 lg:py-3 rounded-lg font-bold text-xs sm:text-sm lg:text-base border border-gray-200 shadow-sm text-center w-full`}
              >
                Level {item.level}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Stats Row - Matching your design */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
        {/* My Streak */}
        <div className="bg-white p-3 sm:p-4 lg:p-6 rounded-lg shadow-sm border">
          <h3 className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mb-2">My Streak</h3>
          <div className="flex items-center gap-2 mb-2">
            <div className="text-orange-500 text-lg sm:text-xl">🔥</div>
            <span className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">8 Days</span>
          </div>
          <button className="text-xs sm:text-sm text-blue-600 hover:underline border border-gray-300 px-2 py-1 rounded">
            + Check in
          </button>
        </div>

        {/* My Points */}
        <div className="bg-white p-3 sm:p-4 lg:p-6 rounded-lg shadow-sm border">
          <h3 className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mb-2">My Points</h3>
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">2,500</div>
        </div>

        {/* My Rank */}
        <div className="bg-white p-3 sm:p-4 lg:p-6 rounded-lg shadow-sm border">
          <h3 className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mb-2">My Rank</h3>
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">325</div>
        </div>
      </div>

      {/* Streak Calendar and Badges Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
        {/* Streak Calendar */}
        <div className="bg-white p-3 sm:p-4 lg:p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-3 sm:mb-4">
            <h3 className="text-sm sm:text-base lg:text-lg font-bold text-gray-900">Streak Calendar</h3>
            <div className="flex items-center gap-1">
              <ChevronLeft className="w-4 h-4 text-gray-400 cursor-pointer hover:text-gray-600" />
              <span className="text-xs sm:text-sm text-gray-600 mx-2">April 2024</span>
              <ChevronRight className="w-4 h-4 text-gray-400 cursor-pointer hover:text-gray-600" />
            </div>
          </div>
          <div className="grid grid-cols-7 gap-1 text-xs text-center mb-2">
            {['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'].map(day => (
              <div key={day} className="p-1 text-gray-500 font-medium">{day}</div>
            ))}
          </div>
          <div className="grid grid-cols-7 gap-1">
            {Array.from({ length: 30 }, (_, i) => (
              <div key={i} className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center">
                {i < 8 ? (
                  <div className="w-4 h-4 sm:w-6 sm:h-6 bg-orange-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">🔥</span>
                  </div>
                ) : (
                  <span className="text-gray-400 text-xs sm:text-sm">{i + 1}</span>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* My Badges */}
        <div className="bg-white p-3 sm:p-4 lg:p-6 rounded-lg shadow-sm border">
          <div className="flex justify-between items-center mb-3 sm:mb-4">
            <h3 className="text-sm sm:text-base lg:text-lg font-bold text-gray-900">My Badges</h3>
            <button className="text-xs sm:text-sm text-blue-600 hover:underline">View All Badges →</button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
            {badges.map((badge, index) => (
              <div key={index} className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg border bg-gradient-to-r from-orange-50 to-yellow-50">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-orange-500 rounded-full flex items-center justify-center">
                  <Trophy className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-xs sm:text-sm font-semibold text-gray-900 truncate">{badge.title}</div>
                  <div className="text-xs text-gray-600 truncate">{badge.subtitle}</div>
                  <div className="text-xs text-gray-500">{badge.progress}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Programs and Challenges Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
        {/* My Active Programs */}
        <div className="bg-white p-3 sm:p-4 lg:p-6 rounded-lg shadow-sm border">
          <h3 className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mb-3 sm:mb-4">My Active Programs</h3>
          {activePrograms.map((program, index) => (
            <div key={index} className="border rounded-lg p-3 sm:p-4">
              <div className="flex gap-3 sm:gap-4 mb-3">
                <img
                  src={program.image}
                  alt={program.title}
                  className="w-12 h-12 sm:w-16 sm:h-16 rounded-lg object-cover flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1 truncate">{program.title}</h4>
                  <div className="flex flex-wrap gap-1 sm:gap-2 text-xs text-gray-600 mb-2">
                    <span className="flex items-center gap-1">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      Challenges: {program.challenges}
                    </span>
                    <span className="flex items-center gap-1">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      Duration: {program.duration}
                    </span>
                    <span className="flex items-center gap-1">
                      <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                      Reward: {program.reward}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5 sm:h-2 mb-1">
                    <div
                      className="bg-blue-600 h-1.5 sm:h-2 rounded-full transition-all duration-300"
                      style={{ width: `${program.progress}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-600">{program.progress}%</div>
                </div>
              </div>
              <button
                onClick={handleUploadImage}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded text-xs sm:text-sm hover:bg-gray-700 transition-colors"
              >
                View
              </button>
            </div>
          ))}
        </div>

        {/* My Active Challenges */}
        <div className="bg-white p-3 sm:p-4 lg:p-6 rounded-lg shadow-sm border">
          <h3 className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mb-3 sm:mb-4">My Active Challenges</h3>
          {activeChallenges.map((challenge, index) => (
            <div key={index} className="border rounded-lg p-3 sm:p-4 relative overflow-hidden">
              <div className="flex gap-3 sm:gap-4 mb-3">
                <img
                  src={challenge.image}
                  alt={challenge.title}
                  className="w-12 h-12 sm:w-16 sm:h-16 rounded-lg object-cover flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1 truncate">{challenge.title}</h4>
                  <div className="flex flex-wrap gap-1 sm:gap-2 text-xs text-gray-600 mb-2">
                    <span className="flex items-center gap-1">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      Duration: {challenge.duration}
                    </span>
                    <span className="flex items-center gap-1">
                      <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                      Reward: {challenge.reward}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5 sm:h-2 mb-1">
                    <div
                      className="bg-green-600 h-1.5 sm:h-2 rounded-full transition-all duration-300"
                      style={{ width: `${challenge.progress}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-600">{challenge.progress}%</div>
                </div>
              </div>
              <div className="flex gap-2">
                <div className="text-xs text-gray-500 flex-1">2/10</div>
                <button className="bg-blue-600 text-white py-1.5 px-3 sm:px-4 rounded text-xs sm:text-sm hover:bg-blue-700 transition-colors">
                  Resume
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
