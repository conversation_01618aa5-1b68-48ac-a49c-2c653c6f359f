"use client"

import { useState } from 'react';
import { ThreeDots, PlusIcon} from '@/utils/icons';
import { useRouter } from 'next/navigation';
import {FilledUserIcon, GreenCheck, RedCheck, LightRedCheck, BrownCheck, BlueCheck, DarkBlueCheck, YellowCheck, PurpleCheck, DarkGreenCheck } from '@/utils/icons';

interface ProgramCardProps {
  id: number
  title: string
  image?: string
  challenges: number
  duration: number
  reward: number
  isFirst?: boolean
}

export function ProgramCard({
  id,
  title,
  image,
  challenges,
  duration,
  reward,
  isFirst = false
}: ProgramCardProps) {
  const router = useRouter();
  const [areasOfLifeIcons, setAreasOfLifeIcons] = useState([
    <GreenCheck key="green" />,
    <RedCheck key="red" />,
    <LightRedCheck key="lightred" />,
    <BrownCheck key="brown" />,
    <BlueCheck key="blue" />,
    <DarkBlueCheck key="darkblue" />,
    <YellowCheck key="yellow" />,
    <PurpleCheck key="purple" />,
    <DarkGreenCheck key="darkgreen" />
  ]);

  const handleCardClick = () => {
    if (id > 0) {
      router.push(`/dashboard/programs/${id}`)
    }
  }

  return (
    <div
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-3 cursor-pointer"
      onClick={handleCardClick}
    >
      {/* Card Header */}
      <div className="flex justify-between items-center py-2">
        <h3 className="text-paragraphContent text-title-md font-bold">
          {isFirst ? 'Welcome to Peakality' : `${id}. ${title}`}
        </h3>
        <div className="text-paragraphContent"><ThreeDots /></div>
      </div>

      {/* Areas of Life */}
      <div className="flex items-center pb-2">
        <div className="text-paragraphContent text-body-medium">Areas of Life:</div>
        <div className="flex -space-x-1 ml-3 items-center">
          {
            areasOfLifeIcons.map((icons, key) => {
              return(
                <div key={key} className='border border-border rounded-full p-1 bg-landingBackground'>{icons}</div>
              )
            })
          }
        </div>
      </div>

      {/* Card Image */}
      {image && (
        <div className="py-1">
          <img
            src={image}
            alt={title}
            className="w-full h-44 object-cover"
          />
        </div>
      )}

      {/* Card Footer */}
      <div className="py-1 flex justify-between items-center">
        {
          id === 0 ? (<div className="flex w-full justify-between text-xs">
            <div>
              <span className='font-bold'>Challenges:</span> {challenges}
            </div>
            <div>
              <span className='font-bold'>Duration:</span> {duration} Days
            </div>
            <div>
              <span className='font-bold'>Reward:</span> {reward} Points
            </div>
          </div>)
            :
            <div className="flex w-full justify-between text-xs max-w-[290px]">
              <div>
                <span className='font-bold'>Steps:</span> {challenges}
              </div>
              <div>
                <span className='font-bold'>Duration:</span> {duration} Days
              </div>
              <div>
                <span className='font-bold'>Reward:</span> {reward} Points
              </div>
            </div>
        }
      </div>

      {/* User Avatars */}
      <div className="py-2 flex justify-between items-center">
        <div className="flex -space-x-2">
          {[...Array(5)].map((_, index) => (
            <div
              key={index}
              className="w-6 h-6 rounded-full bg-landingBackground border border-border flex items-center justify-center"
            >
              <FilledUserIcon />
            </div>
          ))}
          <div className="w-6 h-6 rounded-full bg-landingBackground border border-border flex items-center justify-center ml-1">
            <PlusIcon />
          </div>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation()
            router.push(`/dashboard/programs/${id}`)
          }}
          className="text-xs bg-darkBlueNormal hover:bg-gray-600 text-white px-5 py-2 rounded"
          disabled={id === 0}
        >
          Details
        </button>
      </div>
    </div>
  )
}