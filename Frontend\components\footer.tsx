import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { EmailIcon, LinkedInIcon, TwitterIcon, InstagramIcon, YouTubeIcon, FacebookIcon } from "@/utils/icons"

export function Footer() {
  return (
    <footer className="bg-darkBlueNormal max-md:bg-border text-lightBlue py-10 max-md:pt-0">
      <div className="container">
        <div className="flex flex-col md:flex-row justify-between items-center mb-6 max-md:hidden">
          <Link href="/" className="mb-4 md:mb-0">
            <Image src="/footer-logo.svg" alt="Peakality Logo" width={210} height={30} />
          </Link>

          <div className="flex items-center gap-4">
            <div className="relative flex">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-darkBlueNormal">
                <EmailIcon />
              </span>
              <Input
                type="email"
                placeholder="Enter your email to get the latest news..."
                className="bg-landingBackground text-darkBlueNormal pl-10 min-w-[350px] h-12 border-none focus:ring-0"
              />
            </div>
            <Button className="bg-landingBackground text-darkBlueNormal">Subscribe</Button>
          </div>
        </div>

        <div className="flex justify-center md:justify-between items-center max-md:border-none border-t border-white/20 pt-8 max-md:flex-col max-md:items-center">
          <div className="flex gap-5 justify-center items-center">
            <YouTubeIcon color="lightBlue" />
            <FacebookIcon color="text-lightBlue" />
            <TwitterIcon color="text-lightBlue" />
            <LinkedInIcon color="text-lightBlue" />
            <InstagramIcon color="text-lightBlue" />            
          </div>

          <div className="text-lightBlue text-title-small max-md:text-black max-md:mt-4">Peakality Inc. 2024. All rights reserved.</div>
        </div>
      </div>
    </footer>
  )
}
