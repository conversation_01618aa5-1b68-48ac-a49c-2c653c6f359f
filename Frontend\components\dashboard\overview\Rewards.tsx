import React from 'react';

interface Rewards {
  name: string;
  value: string;
  icon: React.ComponentType;
}

interface RewardsProps {
  stats: Rewards[];
}

export function Rewards({ stats }: RewardsProps) {
  return (
    <div className="bg-white p-4 border border-border rounded-lg">
      <h2 className="text-paragraphContent text-body-lg font-bold mb-2">My Rewards</h2>
      <div className="space-y-2">
        {stats.map((stat, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center min-w-0 flex-1">
              <div className="border border-border rounded-full p-1 bg-landingBackground mr-3 flex-shrink-0">
                <stat.icon />
              </div>
              <span className="text-sm text-gray-700 truncate">{stat.name}</span>
            </div>
            <div className='px-3 bg-landingBackground rounded-full flex-shrink-0'>
              <span className="text-body-medium text-paragraphContent">{stat.value}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 