/**
 * Validates an email address format
 * @param email The email address to validate
 * @returns True if the email is valid, false otherwise
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
}

/**
 * Validates a phone number format
 * @param phone The phone number to validate
 * @returns True if the phone number is valid, false otherwise
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(phone);
}

/**
 * Validates if the input is either a valid email or phone number
 * @param input The input to validate
 * @returns True if the input is a valid email or phone, false otherwise
 */
export function isValidEmailOrPhone(input: string): boolean {
  return isValidEmail(input) || isValidPhone(input);
}

/**
 * Validates password strength
 * @param password The password to validate
 * @returns True if the password meets requirements, false otherwise
 */
export function isStrongPassword(password: string): boolean {
  // At least 8 characters
  if (password.length < 8) return false;
  
  // Check for at least one letter
  const hasLetter = /[a-zA-Z]/.test(password);
  
  // Check for at least one number
  const hasNumber = /[0-9]/.test(password);
  
  // Check for at least one special character
  const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
  
  return hasLetter && hasNumber && hasSpecial;
}

/**
 * Gets password strength feedback
 * @param password The password to check
 * @returns An object with strength score and feedback message
 */
export function getPasswordStrength(password: string): { 
  score: number; 
  message: string;
  color: string;
} {
  if (!password) {
    return { score: 0, message: "Password is required", color: "text-gray-500" };
  }
  
  let score = 0;
  
  // Length check
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  
  // Complexity checks
  if (/[a-z]/.test(password)) score += 1; // lowercase
  if (/[A-Z]/.test(password)) score += 1; // uppercase
  if (/[0-9]/.test(password)) score += 1; // numbers
  if (/[^a-zA-Z0-9]/.test(password)) score += 1; // special chars
  
  // Determine message based on score
  let message = "";
  let color = "";
  
  switch (true) {
    case (score <= 2):
      message = "Weak password";
      color = "text-red-500";
      break;
    case (score <= 4):
      message = "Moderate password";
      color = "text-yellow-500";
      break;
    case (score <= 6):
      message = "Strong password";
      color = "text-green-500";
      break;
    default:
      message = "Very strong password";
      color = "text-green-600";
  }
  
  return { score, message, color };
}
