import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { AreaIcon } from "@/components/dashboard/AreaIcon";
import { ChallengeUserIcon } from "@/utils/icons";

interface ChallengeCardProps {
  title: string;
  image: string;
  areas: string[];
  steps: number;
  duration: number;
  reward: number;
  isPublic: boolean;
  participants: number;
  onDetailsClick?: () => void;
  onResumeClick?: () => void;
}

export function ChallengeCard({
  title,
  image,
  areas,
  steps,
  duration,
  reward,
  isPublic,
  participants,
  onResumeClick,
  onDetailsClick,
}: ChallengeCardProps) {

  
  return (
    <div className="bg-white rounded shadow border p-4 flex flex-col w-[350px] max-md:w-full">
      <div className="flex items-center justify-between mb-2">
        <h2 className="text-title-medium text-paragraphContent font-bold truncate w-full">{title}{isPublic ? "" : "(Unpublished Draft)"}</h2>
        <button className="p-1 rounded hover:bg-gray-100">
          <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="10" cy="10" r="1" /><circle cx="16" cy="10" r="1" /><circle cx="4" cy="10" r="1" /></svg>
        </button>
      </div>
      <div className="mb-1 flex items-center gap-1 h-6">
        <span className="text-body-small text-paragraphContent">Areas of Life:</span> {isPublic ? <div className="flex items-center gap-1">{areas.map((area, i) => <div key={i} className="inline-block w-6 h-6 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center text-xs font-bold border border-white -ml-2 first:ml-0"><AreaIcon key={i} area={area} /></div>)   }</div> : ""}
      </div>
      <div className="w-full overflow-hidden my-2 h-[200px]">
        <Image src={ isPublic ? image : "/challenges/challenge-placeholder.png"} alt={title} width={328} height={200} className="object-cover max-md:w-full h-[200px]" />
      </div>
      <div className="flex flex-wrap items-center text-body-small text-paragraphContent mb-2 gap-2">
        <span> <span className="font-bold">Steps:</span> { isPublic ? steps : "0"}</span>
        <span> <span className="font-bold">Duration:</span> { isPublic ? duration : "0"} Days</span>
        <span> <span className="font-bold">Reward:</span> { isPublic ? reward : "0"} Points</span>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          {Array.from({ length: isPublic ? participants : 0 }).map((_, i) => (
            <span key={i} className="inline-block w-6 h-6 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center text-xs font-bold border border-white -ml-2 first:ml-0"><ChallengeUserIcon /></span>
          ))}
          <span className="inline-block w-6 h-6 rounded-full bg-gray-100 text-gray-500 flex items-center justify-center text-lg font-bold border border-white -ml-2">+</span>
        </div>
        <Button variant="netural" size="sm" className="h-8 text-lightBlue" onClick={ isPublic ? onDetailsClick : onResumeClick}>
          <span className="text-body-medium"> {isPublic ? "Details" : "Resume"}</span>
        </Button>
      </div>
    </div>
  );
} 