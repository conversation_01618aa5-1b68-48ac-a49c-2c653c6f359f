import React from 'react';
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from '@/components/ui/input';
import { Checkbox } from "@/components/ui/checkbox";

export function GetResult() {
    return (
        <div className="w-full max-w-full relative bg-white max-h-[calc(100vh-2rem)] md:max-h-none overflow-y-auto md:overflow-y-visible">
            <div className="relative w-full h-40 max-sm:h-32 md:h-[250px]">
                <Image
                    src="/dashboard/getresult.png"
                    alt="Result background image"
                    fill
                    className="w-full h-full object-cover"
                    priority
                />
            </div>

            <div className="p-3 sm:p-8 max-w-xl mx-auto relative">
                <div className="text-center md:mb-4 text-paragraphContent">
                    <span className="text-2xl md:text-3xl font-bold md:mb-20">Get Your Results</span>
                    <p className="text-sm md:text-body-lg mb-2 md:mb-6 mt-1 md:mt-5">Rhoncus morbi et augue nec, in id ullamcorper at sit.</p>
                </div>
                <form className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <Input type="text" placeholder="First Name" className="border border-gray-500 rounded px-3 py-3 w-full" />
                        <Input type="text" placeholder="Last Name" className="border border-gray-500 rounded px-3 py-3 w-full" />
                        <Input type="text" placeholder="Phone Number" className="border border-gray-500 rounded px-3 py-3 w-full" />
                        <Input type="email" placeholder="Email Address" className="border border-gray-500 rounded px-3 py-3 w-full" />
                    </div>
                    <div className="mt-10">
                        <select className="border-b-2 px-3 py-5 w-full focus:ring-blue-200 max-md:text-sm">
                            <option value="">Which statement best describes you?</option>
                            <option value="1">Vestibulum faucibus odio vitae arcu auctor lectus.</option>
                            <option value="2">Vestibulum faucibus odio vitae arcu auctor lectus.</option>
                        </select>
                    </div>
                    <div className="flex flex-col gap-2 pt-5">
                        <label className="inline-flex items-center">
                            <Checkbox
                                className="border-neturalDark mr-3"
                            />
                            Vestibulum faucibus odio vitae arcu auctor lectus.
                        </label>
                        <label className="inline-flex items-center">
                            <Checkbox
                                className="border-neturalDark mr-3"
                            />
                            Vestibulum faucibus odio vitae arcu auctor lectus.
                        </label>
                    </div>
                    <div className="flex justify-center pt-5 md:pt-10">
                        <Button
                            variant="primary"
                        >
                           <span className="text-white text-title-medium">Send My Results</span>
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    );
} 