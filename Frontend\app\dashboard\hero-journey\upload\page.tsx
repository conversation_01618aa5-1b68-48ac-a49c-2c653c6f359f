"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Upload, ArrowLeft, Camera, Image as ImageIcon } from 'lucide-react';

export default function HeroJourneyUpload() {
  const router = useRouter();
  const [dragActive, setDragActive] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = (file: File) => {
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = () => {
    // Here you would typically upload the image to your server
    console.log('Saving hero image...');
    router.push('/dashboard?tab=hero-journey');
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => router.back()}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span className="text-sm sm:text-base">Back</span>
        </button>
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
          Upload Hero Image
        </h1>
      </div>

      {/* Upload Area */}
      <div className="bg-white rounded-lg shadow-sm border p-6 sm:p-8">
        <div className="text-center mb-6">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
            Add Your Hero's Journey Image
          </h2>
          <p className="text-sm sm:text-base text-gray-600">
            Upload an image that represents your personal journey and growth
          </p>
        </div>

        {/* Upload Zone */}
        <div
          className={`
            relative border-2 border-dashed rounded-lg p-8 sm:p-12 text-center transition-all duration-200
            ${dragActive 
              ? 'border-blue-500 bg-blue-50' 
              : uploadedImage 
                ? 'border-green-500 bg-green-50' 
                : 'border-gray-300 hover:border-gray-400 bg-gray-50'
            }
          `}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          {uploadedImage ? (
            <div className="space-y-4">
              <img
                src={uploadedImage}
                alt="Uploaded hero image"
                className="max-w-full max-h-64 mx-auto rounded-lg shadow-sm"
              />
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <label className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors cursor-pointer">
                  <Camera className="w-4 h-4" />
                  <span className="text-sm">Change Image</span>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileInput}
                    className="hidden"
                  />
                </label>
                <button
                  onClick={handleSave}
                  className="inline-flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Upload className="w-4 h-4" />
                  <span className="text-sm">Save Image</span>
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="mx-auto w-16 h-16 sm:w-20 sm:h-20 bg-gray-200 rounded-full flex items-center justify-center">
                <ImageIcon className="w-8 h-8 sm:w-10 sm:h-10 text-gray-400" />
              </div>
              <div className="space-y-2">
                <p className="text-base sm:text-lg font-medium text-gray-900">
                  Drop your image here, or{' '}
                  <label className="text-blue-600 hover:text-blue-700 cursor-pointer underline">
                    browse
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileInput}
                      className="hidden"
                    />
                  </label>
                </p>
                <p className="text-xs sm:text-sm text-gray-500">
                  Supports: JPG, PNG, GIF up to 10MB
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Tips */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">💡 Tips for a great hero image:</h3>
          <ul className="text-xs sm:text-sm text-blue-800 space-y-1">
            <li>• Choose an image that inspires and motivates you</li>
            <li>• Consider landscapes, achievements, or meaningful moments</li>
            <li>• High-quality images work best (at least 800x600 pixels)</li>
            <li>• Make sure the image reflects your personal journey</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
