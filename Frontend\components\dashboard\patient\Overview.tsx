import React, { useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { usePatientDialog } from '@/contexts/PatientDialogContext';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { SummaryPatientDetail } from '@/components/dashboard/patient/SummaryPatientDetail';
import { WheelOfLife } from '@/components/dashboard/overview/WheelOfLife';
import { DailyHealthStats } from '@/components/dashboard/overview/DailyHealthStats';
import { DailyChallenges } from '@/components/dashboard/overview/DailyChallenges';
import { ChallengeTracker } from '@/components/dashboard/overview/ChallengeTracker';
import { ProgressOverTime } from '@/components/dashboard/overview/ProgressOverTime';
import { Rewards } from '@/components/dashboard/overview/Rewards';
import { DonutChart } from "@/components/dashboard/DonutChart";
import { GreenCheck, RedCheck, BlueCheck, DarkBlueCheck, YellowCheck, PurpleChe<PERSON>, DarkGreenCheck } from '@/utils/icons';

export function Overview() {
  const router = useRouter();
  const { openDialog } = usePatientDialog();
  const userRole = useSelector((state: RootState) => state.user.user?.role);

  // Navigation handlers
  const handleViewDailyChallenges = () => {
    router.push('/dashboard/challenges');
  };

  const handleViewHealthStats = () => {
    router.push('/dashboard');
  };

  const handleRetakeQuiz = () => {
    openDialog();
  };

  // Wheel of Life data
  const wheelOfLifeData = useMemo(() => [
    { name: 'Finance', value: 6, color: '#8BBB4B' },
    { name: 'Romance', value: 5, color: '#CC1F70' },
    { name: 'Family', value: 5, color: '#EE5D60' },
    { name: 'Creative', value: 2, color: '#814679' },
    { name: 'Physical', value: 6, color: '#324D88' },
    { name: 'Social', value: 7, color: '#2472AB' },
    { name: 'Career', value: 7, color: '#2BACA4' },
    { name: 'Emotions', value: 4, color: '#34847D' },
    { name: 'Spiritual', value: 4, color: '#338F5E' },
  ], []);

  // Daily Health Stats
  const healthStats = useMemo(() => ({
    heartRate: 78,
    sleep: '6/8',
    steps: '5,000'
  }), []);

  // Challenge Tracker data
  const challengeTrackerData = useMemo(() => [
    { day: 'Sunday', completed: 4, total: 2 },
    { day: 'Monday', completed: 4, total: 2 },
    { day: 'Tuesday', completed: 4, total: 3 },
    { day: 'Wednesday', completed: 4, total: 2 },
    { day: 'Thursday', completed: 4, total: 1 },
    { day: 'Friday', completed: 4, total: 2 },
    { day: 'Saturday', completed: 4, total: 2 }
  ], []);

  // Progress Over Time data
  const progressOverTimeData = useMemo(() => [
    { month: 'Jan', avgWOL: 2, challengesCompleted: 3 },
    { month: 'Feb', avgWOL: 3, challengesCompleted: 3 },
    { month: 'Mar', avgWOL: 4, challengesCompleted: 5 },
    { month: 'Apr', avgWOL: 3, challengesCompleted: 4 },
    { month: 'May', avgWOL: 6, challengesCompleted: 6 },
    { month: 'Jun', avgWOL: 6, challengesCompleted: 7 },
    { month: 'Jul', avgWOL: 5, challengesCompleted: 6 }
  ], []);

  // Health Stats data
  const healthStatsData = useMemo(() => [
    { name: 'Finance', value: '+8.2%', icon: DarkGreenCheck },
    { name: 'Romance', value: '+8.2%', icon: RedCheck },
    { name: 'Family', value: '+8.2%', icon: BlueCheck },
    { name: 'Creative', value: '+8.2%', icon: YellowCheck },
    { name: 'Physical', value: '+8.2%', icon: GreenCheck },
    { name: 'Social', value: '+8.2%', icon: RedCheck },
    { name: 'Career', value: '+8.2%', icon: DarkBlueCheck },
    { name: 'Emotional', value: '+8.2%', icon: YellowCheck },
    { name: 'Spiritual', value: '+8.2%', icon: PurpleCheck }
  ], []);

  // Top 5 Wheel of Life Categories data
  const topCategories = useMemo(() => [
    { name: 'Emotional', points: '1,200pts', percentage: '+8.2%', color: '#47AEA9', value: 35 },
    { name: 'Spiritual', points: '800pts', percentage: '+7%', color: '#91BF2C', value: 20 },
    { name: 'Family', points: '645pts', percentage: '+2.5%', color: '#A1D431', value: 15 },
    { name: 'Physical', points: '590pts', percentage: '-6.5%', color: '#E2F2BF', value: 15 },
    { name: 'Career', points: '342pts', percentage: '+1.7%', color: '#F1F9E0', value: 15 }
  ], []);

  const tealColors = ["#47AEA9", "#91BF2C", "#A1D431", "#E2F2BF", "#F1F9E0"];

  return (
    <div className="w-full">
      {userRole === "provider" && (
        <SummaryPatientDetail />
      )}

      {/* Top Section - Three Cards */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6 mb-6 w-full">
        <div className='max-md:flex-col gap-4 grid grid-cols-1 xl:grid-cols-2'>
          <WheelOfLife data={wheelOfLifeData} onRetakeQuiz={handleRetakeQuiz} />
          <DailyHealthStats
            heartRate={healthStats.heartRate}
            sleep={healthStats.sleep}
            steps={healthStats.steps}
            onViewHealthStats={handleViewHealthStats}
          />
        </div>
        <DailyChallenges onViewDailyChallenges={handleViewDailyChallenges} />
      </div>

      {/* Charts Section */}
      <div className='grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6 mt-6'>
        <ChallengeTracker data={challengeTrackerData} />
        <ProgressOverTime data={progressOverTimeData} />
      </div>

      {/* Bottom Section */}
      <div className='grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6 mt-6'>
        <Rewards stats={healthStatsData} />
        <div className="bg-white p-4 border border-border rounded-lg">
          <h2 className="text-lg md:text-xl font-bold mb-4">My Top 5 Wheel of Life Categories</h2>
          <div className="w-full">
            <DonutChart
              title=""
              colors={tealColors}
              data={topCategories}
            />
          </div>
        </div>
      </div>
    </div>
  );
}