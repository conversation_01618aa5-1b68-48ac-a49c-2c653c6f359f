import React, { useState, useMemo } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface DailyChallengesProps {
  onViewDailyChallenges: () => void;
}

export function DailyChallenges({ onViewDailyChallenges }: DailyChallengesProps) {
  const todayDate = new Date();
  const [calendarMonth, setCalendarMonth] = useState(todayDate.getMonth());
  const [calendarYear, setCalendarYear] = useState(todayDate.getFullYear());

  const daysOfWeek = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

  const dailyChallenges = useMemo(() => [
    { name: 'My Daily Tasks', value: 8, total: 10, color: '#38B2AC' },
    { name: 'Decision Destination', value: 4, total: 10, color: '#38B2AC' },
    { name: '33 Questions', value: 1, total: 10, color: '#38B2AC' },
  ], []);

  function handlePrevMonth() {
    setCalendarMonth((prev) => {
      if (prev === 0) {
        setCalendarYear((y) => y - 1);
        return 11;
      }
      return prev - 1;
    });
  }

  function handleNextMonth() {
    setCalendarMonth((prev) => {
      if (prev === 11) {
        setCalendarYear((y) => y + 1);
        return 0;
      }
      return prev + 1;
    });
  }

  function getDaysInMonth(month: number, year: number): number {
    return new Date(year, month + 1, 0).getDate();
  }

  function getFirstDayOfWeek(month: number, year: number): number {
    return new Date(year, month, 1).getDay();
  }

  const calendarDays = useMemo(() => {
    const daysInMonth = getDaysInMonth(calendarMonth, calendarYear);
    const firstDayOfMonth = getFirstDayOfWeek(calendarMonth, calendarYear);
    const prevMonth = calendarMonth === 0 ? 11 : calendarMonth - 1;
    const prevMonthYear = calendarMonth === 0 ? calendarYear - 1 : calendarYear;
    const daysInPrevMonth = getDaysInMonth(prevMonth, prevMonthYear);

    const previousMonthDays = Array.from(
      { length: firstDayOfMonth },
      (_, i) => ({
        day: daysInPrevMonth - firstDayOfMonth + i + 1,
        currentMonth: false,
        weekDay: i
      })
    );

    const currentMonthDays = Array.from(
      { length: daysInMonth },
      (_, i) => ({
        day: i + 1,
        currentMonth: true,
        weekDay: (firstDayOfMonth + i) % 7
      })
    );

    return [...previousMonthDays, ...currentMonthDays];
  }, [calendarMonth, calendarYear]);

  return (
    <div className="bg-white p-4 border border-border w-full flex flex-col xl:flex-1">
      <h2 className="text-paragraphContent text-body-lg font-bold mb-2">My Daily Challenges</h2>
      <div className="flex flex-col xl:flex-row gap-4 md:gap-6 h-full font-poppins">
        {/* Calendar Section */}
        <div className="flex-1 min-w-0 xl:max-w-md">
          <div className="flex items-center justify-between mb-3 px-1">
            <button onClick={handlePrevMonth} className="p-2 rounded-full bg-white border border-gray-200 hover:bg-blue-50 transition flex items-center justify-center w-8 h-8 md:w-10 md:h-10 shadow-xl">
              <ChevronLeft size={14} className="md:w-[16px] md:h-[16px]" />
            </button>
            <span className="font-semibold text-gray-700 text-sm md:text-base px-2 text-center">
              {new Date(calendarYear, calendarMonth).toLocaleString('default', { month: 'long', year: 'numeric' })}
            </span>
            <button onClick={handleNextMonth} className="p-2 rounded-full bg-white border border-gray-200 hover:bg-blue-50 transition flex items-center justify-center w-8 h-8 md:w-10 md:h-10 shadow-xl">
              <ChevronRight size={14} className="md:w-[16px] md:h-[16px]" />
            </button>
          </div>
          <div className="grid grid-cols-7 mb-2 gap-1">
            {daysOfWeek.map((day, index) => (
              <div key={index} className="text-center text-body-small text-border font-poppins py-1">
                {day}
              </div>
            ))}
          </div>
          <div className="grid grid-cols-7 gap-1 auto-rows-fr">
            {calendarDays.map((dayObj, index) => {
              const isSunday = dayObj.weekDay === 0;
              const isToday = dayObj.currentMonth && dayObj.day === todayDate.getDate() && calendarMonth === todayDate.getMonth() && calendarYear === todayDate.getFullYear();
              return (
                <div
                  key={index}
                  className={`text-center text-label-large aspect-square h-9 flex items-center justify-center rounded-full font-poppins
                    ${!dayObj.currentMonth
                      ? 'text-gray-300'
                      : isToday
                        ? 'bg-blue-500 text-white font-bold shadow-md'
                        : isSunday && dayObj.currentMonth
                          ? 'text-red-500 font-bold'
                          : 'text-gray-700 hover:bg-gray-50'}
                    ${dayObj.currentMonth && !isToday ? 'cursor-pointer transition-all duration-200' : ''}
                    ${dayObj.currentMonth && isSunday && !isToday ? 'hover:bg-red-200' : ''}
                  `}
                >
                  {dayObj.day}
                </div>
              );
            })}
          </div>
        </div>

        {/* Challenges Section */}
        <div className="flex-1 min-w-0 flex flex-col justify-between xl:pl-4">
          <div className="flex-grow">
            <div className="pt-4 text-title-medium text-netural100 font-poppins">Daily Challenges</div>
            <div className="mt-6 space-y-8">
              {dailyChallenges.map((c, i) => (
                <div key={`challenge-${i}`} className="flex flex-col">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-body-small text-netural100 font-poppins">{c.name}</span>
                    <span className="text-body-small text-netural100 font-poppins">{c.value}/{c.total}</span>
                  </div>
                  <div className="w-full h-[5px] bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full rounded-full transition-all duration-500 ease-in-out"
                      style={{ width: `${(c.value / c.total) * 100}%`, background: c.color }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <Button
            variant="outlinedDark"
            onClick={onViewDailyChallenges}
            className="h-10 px-6 mt-2 mt-4"
          >
            <span className='text-title-medium text-darkBlueNormal'>View Daily Challenges</span>
          </Button>
        </div>
      </div>
    </div>
  );
} 