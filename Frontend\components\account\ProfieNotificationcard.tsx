export function ProfileNotificationCard() {
  return (
    <div className="bg-white rounded border w-[504px] max-md:w-full p-8">
      <div className="text-title-lg text-netural100 font-bold mb-4">
        Desktop notifications
      </div>
      <div className="mb-4">
        <label className="flex items-center gap-2 text-body-large text-netural90">
          <input type="checkbox" defaultChecked />
          <span>Send notifications to my computer</span>
        </label>
      </div>
      <div className="mb-6 ml-8 max-md:ml-0">
        <div className="font-medium mb-2 text-body-large text-netural100">
          File comments
        </div>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="desktop-file-comments" defaultChecked />
          <span>All comments, mentions, and replies</span>
        </label>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="desktop-file-comments" />
          <span>Only mentions and replies</span>
        </label>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="desktop-file-comments" />
          <span>None</span>
        </label>
      </div>
      <div className="text-title-lg text-netural100 font-bold mb-4">
        Email notifications
      </div>
      <div className="mb-4">
        <label className="flex items-center gap-2 text-body-large text-netural90">
          <input type="checkbox" defaultChecked />
          <span>Send notifications by email</span>
        </label>
        <div className="text-body-small text-netural40 ml-6">
          You'll still get other emails from Peakality, like important account
          or billing info.
        </div>
      </div>
      <div className="mb-6 ml-8 max-md:ml-0">
        <div className="font-medium mb-2 text-body-large text-netural100">
          File comments
        </div>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="email-file-comments" defaultChecked />
          <span>All comments, mentions, and replies</span>
        </label>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="email-file-comments" />
          <span>Only mentions and replies</span>
        </label>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="email-file-comments" />
          <span>None</span>
        </label>
      </div>
      <div className="mb-6 ml-8 max-md:ml-0">
        <div className="font-medium mb-2 text-body-large text-netural100">
          Invites and requests
        </div>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="email-invites" defaultChecked />
          <span>All types of invites and requests</span>
        </label>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="email-invites" />
          <span>Only invites and requests that need my response</span>
        </label>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="email-invites" />
          <span>None</span>
        </label>
      </div>
      <div className="mb-6 ml-8 max-md:ml-0">
        <div className="font-medium mb-2 text-body-large text-netural100">
          Community comments
        </div>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="email-community" defaultChecked />
          <span>All comments on my published resources</span>
        </label>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="email-community" />
          <span>Only mentions and replies</span>
        </label>
        <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
          <input type="radio" name="email-community" />
          <span>None</span>
        </label>
      </div>
    </div>
  );
}
