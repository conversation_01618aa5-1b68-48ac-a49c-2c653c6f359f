const express = require('express');
const router = express.Router();
const userController = require('../controllers/user.controller');
const { protect } = require('../middleware/auth.middleware');
const { userValidation } = require('../middleware/validation.middleware');

// All routes are protected
router.use(protect);

// User routes
router.get('/profile', userController.getProfile);
router.patch('/profile', userValidation.updateProfile, userController.updateProfile);
router.delete('/profile', userController.deleteProfile);

module.exports = router; 