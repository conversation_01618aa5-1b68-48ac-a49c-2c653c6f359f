import React from 'react';
import { PolarArea } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  RadialLinearScale,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend as ChartLegend,
  Plugin
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Button } from '@/components/ui/button';

// Register Chart.js components
ChartJS.register(RadialLinearScale, ArcElement, ChartTooltip, ChartLegend, ChartDataLabels);

// Custom plugin for curved label lines and always visible numbers with hover effects
const polarAreaLabelLines: Plugin = {
  id: 'polarAreaLabelLines',
  afterDraw(chart: any) {
    const { ctx, data } = chart;
    const meta = chart.getDatasetMeta(0);

    let activeIndex = -1;
    if (chart.tooltip && chart.tooltip._active && chart.tooltip._active.length) {
      activeIndex = chart.tooltip._active[0].index;
    }

    meta.data.forEach((arc: any, i: number) => {
      const model = arc.getProps(['startAngle', 'endAngle', 'outerRadius', 'x', 'y'], true);
      const angle = (model.startAngle + model.endAngle) / 2;

      const isActive = i === activeIndex;
      const r1 = model.outerRadius - (isActive ? 2 : 4);
      const r2 = model.outerRadius + (isActive ? 22 : 18);
      const r3 = model.outerRadius + (isActive ? 38 : 32);

      const x1 = model.x + Math.cos(angle) * r1;
      const y1 = model.y + Math.sin(angle) * r1;
      const cx = model.x + Math.cos(angle) * r2;
      const cy = model.y + Math.sin(angle) * r2;
      const x2 = model.x + Math.cos(angle) * r3;
      const y2 = model.y + Math.sin(angle) * r3;

      ctx.save();
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.quadraticCurveTo(cx, cy, x2, y2);

      let color = '#222';
      if (Array.isArray(data.datasets[0].backgroundColor)) {
        color = data.datasets[0].backgroundColor[i] as string || '#222';
      } else if (typeof data.datasets[0].backgroundColor === 'string') {
        color = data.datasets[0].backgroundColor;
      }

      ctx.strokeStyle = isActive ? color : color;
      ctx.lineWidth = isActive ? 2 : 1.2;
      ctx.stroke();

      ctx.font = isActive ? 'bold 20px Inter, Arial' : 'bold 18px Inter, Arial';
      ctx.fillStyle = isActive ? color : color;
      ctx.textAlign = x2 > model.x ? 'left' : 'right';
      ctx.textBaseline = 'middle';

      let value = '';
      if (Array.isArray(data.datasets[0].data)) {
        value = String(data.datasets[0].data[i]);
      } else {
        value = String(data.datasets[0].data);
      }

      const displayValue = isActive ? `${value}/10` : value;
      ctx.fillText(displayValue, x2 + (x2 > model.x ? 8 : -8), y2);

      if (isActive && data.labels && data.labels[i]) {
        ctx.font = '12px Inter, Arial';
        ctx.fillStyle = '#666';
        ctx.fillText(data.labels[i], x2 + (x2 > model.x ? 8 : -8), y2 + 20);
      }

      ctx.restore();
    });
  }
};

ChartJS.register(polarAreaLabelLines);

interface WheelOfLifeProps {
  data: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  onRetakeQuiz: () => void;
}

export function WheelOfLife({ data, onRetakeQuiz }: WheelOfLifeProps) {
  return (
    <div className="bg-white p-4 border border-border xl:mx-0 flex flex-col">
      <h2 className="text-paragraphContent text-body-lg font-bold mb-2">My Wheel of Life Summary</h2>
      <div className="flex justify-center relative w-full">
        <div className="w-full max-w-[224px] h-[169px] mx-auto">
          <PolarArea
            data={{
              labels: data.map((d) => d.name),
              datasets: [
                {
                  data: data.map((d) => d.value),
                  backgroundColor: data.map((d) => d.color),
                  borderWidth: 0,
                  borderColor: '#fff',
                  hoverBackgroundColor: data.map((d) => d.color.replace(')', ', 0.8)').replace('rgb', 'rgba')),
                  hoverBorderColor: '#fff',
                  hoverBorderWidth: 2,
                },
              ],
            }}
            options={{
              plugins: {
                legend: { display: false },
                tooltip: {
                  enabled: true,
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  titleColor: '#333',
                  bodyColor: '#333',
                  borderColor: '#ddd',
                  borderWidth: 1,
                  padding: 10,
                  cornerRadius: 8,
                  displayColors: true,
                  callbacks: {
                    label: function (context: any) {
                      return `${context.label}: ${context.raw}/10`;
                    }
                  }
                },
                datalabels: { display: false },
              },
              scales: {
                r: {
                  angleLines: { display: false },
                  grid: { display: false },
                  pointLabels: { display: false },
                  ticks: { display: false },
                  min: 0,
                  max: 10,
                },
              },
              responsive: true,
              maintainAspectRatio: false,
              layout: {
                padding: 0
              },
              onHover: (event: any, elements: any) => {
                if (event.native) {
                  event.native.target.style.cursor = elements.length ? 'pointer' : 'default';
                }
              }
            }}
            plugins={[polarAreaLabelLines]}
          />
        </div>
      </div>
      <div className="grid grid-cols-3 gap-x-2 gap-y-1 my-2 w-full text-body-small justify-center">
        {data.map((category, index) => (
          <div
            key={index}
            className="flex items-center p-1 rounded-md hover:bg-gray-50 cursor-pointer transition-colors duration-200"
          >
            <span className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: category.color }}></span>
            <span className="truncate text-[#000000B2] font-inter">{category.name}</span>
          </div>
        ))}
      </div>
      <div className='flex mx-auto'>
        <Button
          variant="outlinedDark"
          onClick={onRetakeQuiz}
          className="h-10 px-6 mt-2"
        >
          <span className='text-title-medium text-darkBlueNormal'>Retake Quiz</span>
        </Button>
      </div>
    </div>
  );
} 