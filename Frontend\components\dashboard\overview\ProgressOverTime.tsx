import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  ResponsiveContainer,
} from 'recharts';

interface ProgressOverTimeProps {
  data: Array<{
    month: string;
    avgWOL: number;
    challengesCompleted: number;
  }>;
}

export function ProgressOverTime({ data }: ProgressOverTimeProps) {
  return (
    <div className="bg-white p-4 rounded-lg border">
      <h2 className="text-paragraphContent text-body-lg font-bold mb-2">My Progress Over Time</h2>
      <div className="h-[250px] md:h-[300px] lg:h-[350px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{ top: 20, right: 0, left: -20, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" vertical={true} horizontal={true} />
            <XAxis
              dataKey="month"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6B7280' }}
              dy={10}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              domain={[0, 8]}
              ticks={[0, 2, 4, 6, 8]}
              tick={{ fontSize: 12, fill: '#6B7280' }}
            />
            <Tooltip
              cursor={{ stroke: '#ddd', strokeWidth: 1, strokeDasharray: '3 3' }}
              contentStyle={{ borderRadius: '8px', border: 'none', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}
              formatter={(value, name) => [
                `${value}`,
                name === 'avgWOL' ? 'Avg WOL Response' : 'Challenges Completed'
              ]}
            />
            <Line
              type="linear"
              dataKey="avgWOL"
              stroke="#4FD1C5"
              strokeWidth={2}
              dot={{ r: 4, fill: "#4FD1C5", strokeWidth: 0 }}
              activeDot={{ r: 6, fill: "#4FD1C5", strokeWidth: 0 }}
              connectNulls={true}
            />
            <Line
              type="linear"
              dataKey="challengesCompleted"
              stroke="#F56565"
              strokeWidth={2}
              dot={{ r: 4, fill: "#F56565", strokeWidth: 0 }}
              activeDot={{ r: 6, fill: "#F56565", strokeWidth: 0 }}
              connectNulls={true}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      <div className="flex justify-center mt-4 space-x-6">
        <div className="flex items-center">
          <div className="w-2 h-1 bg-[#4FD1C5]"></div>
          <div className="w-2 h-2 bg-[#4FD1C5] rounded-full"></div>
          <div className="w-2 h-1 bg-[#4FD1C5] mr-2"></div>
          <span className="text-xs text-gray-600">Avg WOL Response</span>
        </div>
        <div className="flex items-center">
          <div className="w-2 h-1 bg-[#F56565]"></div>
          <div className="w-2 h-2 bg-[#F56565] rounded-full"></div>
          <div className="w-2 h-1 bg-[#F56565] mr-2"></div>
          <span className="text-xs text-gray-600">Challenges Completed</span>
        </div>
      </div>
    </div>
  );
} 