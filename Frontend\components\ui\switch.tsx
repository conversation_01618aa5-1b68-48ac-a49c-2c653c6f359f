"use client"

import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"
import { cn } from "@/lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => {
  const [checked, setChecked] = React.useState(props.checked ?? false);

  // Keep local state in sync with controlled prop
  React.useEffect(() => {
    if (typeof props.checked === "boolean") setChecked(props.checked);
  }, [props.checked]);

  return (
    <SwitchPrimitives.Root
      className={cn(
        "peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",
        checked ? "bg-teal-500" : "bg-gray-200",
        className
      )}
      checked={checked}
      onCheckedChange={v => {
        setChecked(v);
        props.onCheckedChange?.(v);
      }}
      {...props}
      ref={ref}
    >
      <SwitchPrimitives.Thumb
        className={cn(
          "pointer-events-none block h-5 w-5 rounded-full shadow-lg ring-0 transition-transform",
          checked
            ? "translate-x-5 bg-white flex items-center justify-center"
            : "translate-x-0 bg-gray-500 flex items-center justify-center"
        )}
      >
        {checked ? (
          // Checkmark SVG
          <svg width="16" height="16" viewBox="0 0 16 16" className="mx-auto" fill="none">
            <path d="M4 8.5L7 11.5L12 5.5" stroke="#23205F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        ) : (
          // X SVG
          <svg width="16" height="16" viewBox="0 0 16 16" className="mx-auto" fill="none">
            <path d="M5 5L11 11M11 5L5 11" stroke="#F3F3F3" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        )}
      </SwitchPrimitives.Thumb>
    </SwitchPrimitives.Root>
  );
});
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }
