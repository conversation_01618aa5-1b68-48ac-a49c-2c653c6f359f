{"name": "peakality-backend", "version": "1.0.0", "description": "Backend for Peakality application", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.1", "morgan": "^1.10.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.3"}}