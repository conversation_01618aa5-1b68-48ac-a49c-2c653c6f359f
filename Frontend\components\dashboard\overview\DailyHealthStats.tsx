import React from 'react';
import { Heart, Zap, Flag } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface DailyHealthStatsProps {
  heartRate: number;
  sleep: string;
  steps: string;
  onViewHealthStats: () => void;
}

export function DailyHealthStats({ heartRate, sleep, steps, onViewHealthStats }: DailyHealthStatsProps) {
  return (
    <div className="bg-white p-4 border border-border flex flex-col w-full">
      <h2 className="text-paragraphContent text-body-lg font-bold mb-2">My Daily Health Stats</h2>
      <div className="space-y-2 md:space-y-4 w-full mt-3 flex-grow font-dmSans">
        <div className="bg-red-50 pt-2 px-4 rounded-2xl h-18 flex flex-col text-wheelSocial">
          <div className="text-title-medium flex justify-start gap-1 w-full">
            <span><Heart size={12} className="md:w-5 md:h-5" /></span>
            <span>Heart Rate</span>
          </div>
          <div className="flex items-baseline justify-center">
            <span className=" text-headline-large font-bold">{heartRate}</span>
            <span className="text-body-large ml-1">bpm</span>
          </div>
        </div>
        <div className="bg-blue-50 pt-2 px-4 rounded-2xl h-18 flex flex-col text-[#365697]">
          <div className="text-title-medium flex justify-start gap-1 w-full">
            <span><Zap size={12} className="md:w-5 md:h-5" /></span>
            <span>Sleep</span>
          </div>
          <div className="flex items-baseline justify-center">
            <span className="text-headline-large font-bold">{sleep}</span>
            <span className="text-body-large ml-1">steps</span>
          </div>
        </div>
        <div className="bg-green-50 pt-2 px-4 rounded-2xl h-18 flex flex-col">
          <div className="text-title-medium text-wheelPhysical flex justify-start gap-1 w-full">
            <span><Flag size={12} className="md:w-5 md:h-5" /></span>
            <span>Activity</span>
          </div>
          <div className="flex items-baseline justify-center">
            <span className="text-headline-large font-bold text-wheelPhysical">{steps}</span>
            <span className="text-body-large text-wheelPhysical ml-1">steps</span>
          </div>
        </div>
      </div>
      <Button
        variant="outlinedDark"
        onClick={onViewHealthStats}
        className="h-10 px-6 mt-2 mx-auto"
      >
        <span className='text-title-medium text-darkBlueNormal'>View Health Stats</span>
      </Button>
    </div>
  );
} 