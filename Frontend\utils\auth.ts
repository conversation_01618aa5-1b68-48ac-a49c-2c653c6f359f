import { UserData } from '@/store/userSlice';

export const PATIENT_EMAIL = '<EMAIL>';
export const PROVIDER_EMAIL = '<EMAIL>';

export const handleLogin = (email: string, password: string): UserData | null => {
  if (email === PATIENT_EMAIL) {
    return {
      role: 'patient',
      email: PATIENT_EMAIL,
      username: 'Patient User'
    };
  } else if (email === PROVIDER_EMAIL) {
    return {
      role: 'provider',
      email: PROVIDER_EMAIL,
      username: 'Provider User'
    };
  }
  return null;
};

export const handleSignup = (email: string, password: string, isProvider: boolean): UserData => {
  return {
    role: isProvider ? 'provider' : 'patient',
    email,
    username: email.split('@')[0]
  };
};

export const getRedirectPath = (userData: UserData | null): string => {
  if (!userData) return '/login';
  
  switch (userData.role) {
    case 'patient':
      return '/dashboard';
    case 'provider':
      return '/dashboard';
    default:
      return '/login';
  }
}; 