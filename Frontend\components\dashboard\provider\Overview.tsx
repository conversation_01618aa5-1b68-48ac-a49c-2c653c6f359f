"use client"

import { useState, useEffect } from "react"
import { CircularProgressChart } from "@/components/dashboard/CircularProgressChart"
import { DonutChart } from "@/components/dashboard/DonutChart"

export function Overview() {
  const [activeTab, setActiveTab] = useState("overview")
  const [isMobile, setIsMobile] = useState(false)

  // Define the colors for the charts
  const tealColors = ["#E2F2BF", "#799F25", "#A1D431", "#91BF2C", "#799F25"];
  const goldColors = ["#EDBA29", "#B28C1F", "#D5A725", "#F9EABD", "#BE9521"];

  // Check window width on client side only
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Set initial value
    handleResize()

    // Add event listener
    window.addEventListener('resize', handleResize)

    // Clean up
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div>
      <div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-5">
          <CircularProgressChart
            title="Active vs Inactive Patients"
            percentage={67}
            color="#3ABAB4"
            linkText="My Patients"
            linkHref="/dashboard/patients"
            legend={[
              { label: "Inactive", color: "#3ABAB4" },
              { label: "Active", color: "#E5E7EB" }
            ]}
          />

          <CircularProgressChart
            title="Patient Programs"
            percentage={67}
            color="#D4A72C"
            linkText="My Programs"
            linkHref="/dashboard/programs"
            legend={[
              { label: "Started", color: "#D4A72C" },
              { label: "Completed", color: "#E5E7EB" }
            ]}
          />

          <CircularProgressChart
            title="Patient Challenges"
            percentage={67}
            color="#4F6D8F"
            linkText="My Challenges"
            linkHref="/dashboard/challenges"
            legend={[
              { label: "Started", color: "#4F6D8F" },
              { label: "Completed", color: "#E5E7EB" }
            ]}
          />
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
          <DonutChart
            title="Struggling Patients"
            colors={goldColors}
            data={[
              { name: "Sarah Smith", points: "1,200pts", percentage: "****%", color: goldColors[0] },
              { name: "Max Johnson", points: "800pts", percentage: "+7%", color: goldColors[1] },
              { name: "Emily Park", points: "645pts", percentage: "****%", color: goldColors[2] },
              { name: "Alex Chen", points: "590pts", percentage: "-6.5%", color: goldColors[3] },
              { name: "Ryan Taylor", points: "342pts", percentage: "****%", color: goldColors[4] }
            ]}
          />

          <DonutChart
            title="Performing Patients"
            colors={tealColors}
            data={[
              { name: "Olivia White", points: "1,200pts", percentage: "****%", color: tealColors[0] },
              { name: "Dylan Patel", points: "800pts", percentage: "+7%", color: tealColors[1] },
              { name: "Grace Thompson", points: "645pts", percentage: "****%", color: tealColors[2] },
              { name: "Ethan Martinez", points: "590pts", percentage: "-6.5%", color: tealColors[3] },
              { name: "Jasmine Rogers", points: "342pts", percentage: "****%", color: tealColors[4] }
            ]}
          />
        </div>
      </div>
    </div>
  )
}
