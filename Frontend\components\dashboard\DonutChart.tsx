"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip
} from "recharts"

interface PatientData {
  name: string
  points: string
  percentage: string
  color: string
}

interface DonutChartProps {
  title: string
  data: PatientData[]
  colors: string[]
  type: string
}

// Custom tooltip component
const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-2 border border-gray-200 rounded-md shadow-sm">
        <p className="text-sm font-medium">{payload[0].name}</p>
        <p className="text-sm">{`${payload[0].value} pts`}</p>
      </div>
    );
  }

  return null;
};

export function DonutChart({
  title,
  data,
  colors,
  type
}: DonutChartProps) {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Initial check
    checkIfMobile()

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile)

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Transform data for the chart
  const chartData = data.map(item => ({
    name: item.name,
    value: parseInt(item.points.replace(/,/g, '')),
    color: item.color
  }))

  // If the chart is for 'Performing Patients', use a single teal color for all segments
  const isTealChart = title === 'Performing Patients';
  const singleTeal = '#55BDB3'; // Use the teal color from the image
  const chartColors = isTealChart ? Array(chartData.length).fill(singleTeal) : colors;

  return (
    <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm">
      {title &&
        <h3 className="text-oldN30 text-body-lg font-bold mb-4 max-md:text-center">{title}</h3>
      }
      <div className="flex max-2xl:flex-col items-center md:justify-between">
        <div className="w-[240px] h-[240px] flex-shrink-0 flex items-center justify-center">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              {/* Outer ring */}
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={75}
                outerRadius={110}
                dataKey="value"
                stroke="#fff"
                strokeWidth={1}
                paddingAngle={0.1}
                isAnimationActive={true}
              >
                {chartData.map((_, index) => (
                  <Cell
                    key={`outer-cell-${index}`}
                    fill={chartColors[index % chartColors.length]}
                  />
                ))}
              </Pie>
              {/* Inner ring */}
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={55}
                outerRadius={80}
                dataKey="value"
                stroke="#fff"
                strokeWidth={1}
                paddingAngle={0.1}
                isAnimationActive={true}
              >
                {chartData.map((_, index) => (
                  <Cell
                    key={`inner-cell-${index}`}
                    fill={chartColors[index % chartColors.length]}
                  />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="flex-1 pl-6 max-md:pl-0 space-y-3 w-full md:max-w-[450px]">
          {data.map((item, index) => (
            <div key={index} className="flex items-center justify-between min-h-[28px]">
              <div className="flex justify-between items-center w-full max-w-[300px]">
                <div className="flex items-center min-w-[120px]">
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-body-large text-paragraphContent mr-2">{item.name}</span>
                </div>
                <span className="text-body-large text-coolGray60 mr-2">{item.points}</span>
              </div>
              <span className={`text-body-medium px-4 py-1 rounded-full text-center ${item.percentage.startsWith('+')
                ? 'bg-landingBackground'
                : item.percentage.startsWith('-')
                  ? 'bg-coolGray60 text-white'
                  : 'text-paragraphContent'
                }`}>
                {item.percentage}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
