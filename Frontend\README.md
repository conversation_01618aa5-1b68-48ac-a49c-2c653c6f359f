# Peakality

A modern patient management and account workflow web application built with Next.js, React, and Tailwind CSS. Peakality streamlines healthcare management by providing an intuitive interface for patient care coordination, billing, and account management.

## Features

### Patient Management
- Comprehensive patient database with advanced search and filtering
- Multi-step patient onboarding workflow:
  - Personal details collection
  - Treatment plan creation
  - Payment processing
  - Patient portal invitation
- Patient history tracking and medical records management
- Appointment scheduling and management with calendar integration
- Treatment progress monitoring with data visualization
- Drag-and-drop interface for task management

### Account Management
- Secure user authentication and authorization
- Profile management with photo upload capability
- Billing and subscription management
- Notification preferences and settings
- Role-based access control
- Theme customization (light/dark mode)

### User Interface
- Responsive design for all devices
- Modern, accessible UI components
- Interactive data visualizations using Chart.js and Recharts
- Real-time updates and notifications
- Intuitive navigation and user flows
- Loading skeletons for better UX
- Toast notifications for user feedback
- Form validation with Zod
- Date handling with date-fns and dayjs

## Tech Stack

### Frontend
- [Next.js](https://nextjs.org/) (App Router) - React framework for production
- [React](https://react.dev/) - UI library
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [Framer Motion](https://www.framer.com/motion/) - Animation library
- [TypeScript](https://www.typescriptlang.org/) - Type-safe JavaScript
- [Shadcn/ui](https://ui.shadcn.com/) - Reusable UI components
- [Radix UI](https://www.radix-ui.com/) - Unstyled, accessible components
- [Material UI](https://mui.com/) - React component library
- [Redux Toolkit](https://redux-toolkit.js.org/) - State management
- [React Hook Form](https://react-hook-form.com/) - Form handling
- [Zod](https://zod.dev/) - Schema validation

### Development Tools
- ESLint - Code linting
- Prettier - Code formatting
- TypeScript - Static type checking
- PostCSS - CSS processing
- Tailwind CSS - Utility-first CSS
- Next.js - Development server and build tools

## Getting Started

### Prerequisites
- Node.js (v18 or higher recommended)
- npm, yarn, or pnpm
- Git
- Modern web browser

### Installation
```bash
# Clone the repository
git clone https://github.com/your-username/peakality.git
cd peakality

# Install dependencies
npm install
# or
yarn install
# or
pnpm install
```

### Environment Setup
Create a `.env.local` file in the root directory with the following variables:
```env
NEXT_PUBLIC_API_URL=your_api_url
NEXT_PUBLIC_APP_URL=http://localhost:3000
DATABASE_URL=your_database_url
NEXTAUTH_SECRET=your_auth_secret
NEXTAUTH_URL=http://localhost:3000
```

### Development
```bash
# Start development server
npm run dev
# or
yarn dev
# or
pnpm dev
```
Visit [http://localhost:3000](http://localhost:3000) to view the app.

### Available Scripts
```bash
# Development
npm run dev        # Start development server
npm run lint       # Run ESLint
npm run build      # Build for production
npm run start      # Start production server
```

## Project Structure
```
peakality/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── dashboard/         # Dashboard pages
│   │   ├── account/      # Account management
│   │   └── patients/     # Patient management
│   └── layout.tsx        # Root layout
├── components/            # Reusable UI components
│   ├── ui/               # Basic UI components
│   └── forms/            # Form components
├── contexts/             # React contexts
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── public/               # Static assets
├── store/                # Redux store configuration
├── styles/               # Global styles
└── utils/                # Utility functions
```

## Key Dependencies

### UI Components
- @radix-ui/* - Accessible UI primitives
- @mui/material - Material Design components
- @dnd-kit/* - Drag and drop functionality
- embla-carousel-react - Carousel component
- react-calendar - Calendar component
- react-loading-skeleton - Loading states

### State Management
- @reduxjs/toolkit - Redux state management
- react-redux - React bindings for Redux
- redux-persist - Redux state persistence

### Data Visualization
- chart.js - Charting library
- react-chartjs-2 - React wrapper for Chart.js
- recharts - Composable charting library

### Form Handling
- react-hook-form - Form validation
- @hookform/resolvers - Form resolvers
- zod - Schema validation

### Utilities
- date-fns - Date manipulation
- dayjs - Date handling
- clsx - Class name utilities
- tailwind-merge - Tailwind class merging
- framer-motion - Animations

## Development Guidelines

### Code Style
- Follow TypeScript best practices
- Use ESLint and Prettier for code formatting
- Write meaningful commit messages
- Document complex functions and components
- Use proper type definitions
- Follow React best practices and hooks guidelines

### Git Workflow
1. Create a new branch for each feature/fix
2. Write clear commit messages
3. Create pull requests for code review
4. Ensure all tests pass before merging

### Component Development
- Use functional components with hooks
- Implement proper TypeScript types
- Follow accessibility guidelines
- Use proper error boundaries
- Implement loading states
- Add proper documentation

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Pull Request Process
1. Update the README.md with details of changes if needed
2. Update the documentation
3. Ensure all tests pass
4. Get code review approval

## Deployment

### Production Deployment
1. Build the application
2. Set up environment variables
3. Configure the web server
4. Deploy to your hosting platform

### CI/CD
- Automated testing on pull requests
- Automated deployment on main branch
- Environment-specific configurations

## Support

For support, email <EMAIL> or join our Slack channel.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Thanks to all contributors
- Inspired by modern healthcare management systems
- Built with the support of the open-source community
- Special thanks to the Next.js, React, and Tailwind CSS teams