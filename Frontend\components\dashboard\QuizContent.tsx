import React, { useState, useEffect } from 'react';
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { GetResult } from './GetResult';

interface QuizContentProps {
    onBack: () => void;
}

// Define the quiz steps (9 steps, no summary)
const quizSteps = [
    {
        title: "Spiritually",
        img: "/dashboard/spiritually.png",
        description: "Is a spiritual practice important to you? How connected do you feel spiritually?",
    },
    {
        title: "Financially",
        img: "/dashboard/financially.png",
        description: "How secure and confident do you feel about money and your finances?"
    },
    {
        title: "Romantically",
        img: "/dashboard/romantically.png",
        description: "How fulfilled are you in your love life?"
    },
    {
        title: "Family",
        img: "/dashboard/family.png",
        description: "How happy are you with your relationships with your children and/or parents?"
    },
    {
        title: "Creatively",
        img: "/dashboard/creatively.png",
        description: "Are you learning anything new intellectually or expressing yourself creatively?"
    },
    {
        title: "Physically",
        img: "/dashboard/physically.png",
        description: "How satisfied are you with your physical health? Do you exercise and eating healthy regularly?"
    },
    {
        title: "Socially",
        img: "/dashboard/socially.png",
        description: "How happy are you with your social life?"
    },
    {
        title: "Professionally",
        img: "/dashboard/professionally.png",
        description: "How satisfied are you with your professional career, academic or volunteer work?"
    },
    {
        title: "Emotionally",
        img: "/dashboard/emotionally.png",
        description: "How well do you process, regulate, and control your emotions? How do you feel on a regular basis?"
    }
];

export function QuizContent({ onBack }: QuizContentProps) {
    const [currentStepIndex, setCurrentStepIndex] = useState(0);
    const [ratings, setRatings] = useState<Array<number | null>>(Array(quizSteps.length).fill(null));
    const [selectedRating, setSelectedRating] = useState<number | null>(ratings[currentStepIndex]);
    const [direction, setDirection] = useState(0);
    const [showResults, setShowResults] = useState(false);

    const currentStep = quizSteps[currentStepIndex];

    // Number button navigation
    const handleNumberButtonClick = (index: number) => {
        setDirection(index > currentStepIndex ? 1 : -1);
        setCurrentStepIndex(index);
        setSelectedRating(ratings[index]);
        // If user clicks the 10th button (index 9), show results
        if (index === 9) {
            setShowResults(true);
        }
    };

    const handleRatingSelect = (rating: number) => {
        const newRatings = [...ratings];
        newRatings[currentStepIndex] = rating;
        setRatings(newRatings);
        setSelectedRating(rating);
    };

    const handleNext = () => {
        setDirection(1);
        // If on the last step (index 8), show results
        if (currentStepIndex === quizSteps.length - 1) {
            setShowResults(true);
        } else {
            setCurrentStepIndex(currentStepIndex + 1);
        }
    };

    const handleBack = () => {
        setDirection(-1);
        if (currentStepIndex > 0) {
            setCurrentStepIndex(currentStepIndex - 1);
        } else {
            onBack();
        }
    };

    useEffect(() => {
        setSelectedRating(ratings[currentStepIndex]);
    }, [currentStepIndex, ratings]);

    const slideVariants = {
        enter: (direction: number) => ({
            x: direction > 0 ? 1000 : -1000,
            opacity: 0
        }),
        center: {
            zIndex: 1,
            x: 0,
            opacity: 1
        },
        exit: (direction: number) => ({
            zIndex: 0,
            x: direction < 0 ? 1000 : -1000,
            opacity: 0
        })
    };

    if (showResults) {
        return <GetResult />;
    }

    return (
        <div className="w-full max-w-full">
            {/* Close Button */}
            <AnimatePresence initial={false} custom={direction} mode="wait">
                <motion.div
                    key={currentStepIndex}
                    custom={direction}
                    variants={slideVariants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    transition={{
                        x: { type: "spring", stiffness: 300, damping: 30 },
                        opacity: { duration: 0.2 }
                    }}
                    className="relative w-full h-40 sm:h-48 md:h-[350px]"
                >
                    <Image
                        src={currentStep.img}
                        alt="Quiz background image"
                        fill
                        className="w-full h-full object-cover rounded-t-lg"
                        priority
                    />
                </motion.div>
            </AnimatePresence>

            <div className="p-3 sm:p-6">
                <DialogHeader>
                    <DialogTitle className="text-center mb-4">
                        <span className='text-title-lg sm:text-headline-large font-bold text-paragraphContent'>{currentStep.title}</span>
                    </DialogTitle>
                    <DialogDescription className="text-center mb-6">
                        <span className="text-center text-body-md sm:text-body-lg text-paragraphContent">{currentStep.description}</span>
                    </DialogDescription>
                </DialogHeader>

                {/* 1-10 Number Navigation */}
                <div className="flex flex-wrap justify-center mb-6 sm:border sm:border-oldBlue md:rounded-full overflow-hidden mt-5 max-w-full sm:max-w-[750px] mx-auto">
                    {[...Array(10)].map((_, index) => (
                        <button
                            key={index}
                            onClick={() => handleNumberButtonClick(index)}
                            className={`flex-1 min-w-[60px] md:min-w-[36px] h-10 sm:h-12 max-sm:rounded-full flex items-center justify-center text-xs sm:text-sm font-medium transition-colors duration-200 ${index < 10 ? 'max-sm:border md:border-r border-oldBlue' : ''} ${currentStepIndex === index ? 'bg-blue-50 bg-gray-200' : 'text-paragraphContent hover:bg-gray-200 focus:bg-gray-200'}`}
                        >
                            {index + 1}
                        </button>
                    ))}
                </div>

                {/* Navigation and Progress */}
                <div className="flex flex-row justify-between items-center mb-4 max-w-full sm:max-w-[800px] w-full mx-auto mt-8 gap-2 md:gap-4">
                    <Button
                        variant="text"
                        leftIcon={<ChevronLeft />}
                        onClick={handleBack}
                        disabled={currentStepIndex === 0}
                        className="text-paragraphContent text-title-sm md:text-title-md px-1 sm:px-4"
                    >
                        Back
                    </Button>
                    <div className="flex space-x-2 md:space-x-4">
                        {quizSteps.map((_, index) => (
                            <span
                                key={index}
                                className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${index === currentStepIndex ? 'bg-oldBlue' : 'border border-gray-300'}`}
                            />
                        ))}
                    </div>
                    <Button
                        variant="text"
                        rightIcon={<ChevronRight />}
                        onClick={handleNext}
                        className="text-paragraphContent text-title-sm md:text-title-md px-1 sm:px-4"
                    >
                        Skip
                    </Button>
                </div>
            </div>
        </div>
    );
} 