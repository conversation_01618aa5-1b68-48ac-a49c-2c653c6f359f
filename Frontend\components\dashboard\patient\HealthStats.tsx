import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, BlueCheck, DarkBlueCheck, YellowCheck, PurpleCheck, DarkGreenCheck } from '@/utils/icons';
import { Donut<PERSON><PERSON> } from "@/components/dashboard/DonutChart";
import {
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';

// Static data - moved outside component to prevent recreation on each render
const health_stats_data = [
  { name: 'Melatonin', value: '+8.2%', icon: DarkGreenCheck },
  { name: 'Il-6', value: '+8.2%', icon: RedCheck },
  { name: 'Growth Hormone', value: '+8.2%', icon: BlueCheck },
  { name: 'Cortisol', value: '+8.2%', icon: YellowCheck },
  { name: 'Blood Pressure', value: '+8.2%', icon: GreenCheck },
  { name: 'Heart Rate', value: '+8.2%', icon: <PERSON><PERSON><PERSON><PERSON> },
  { name: 'B<PERSON>', value: '+8.2%', icon: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> },
  { name: 'Cholesterol', value: '+8.2%', icon: YellowCheck },
  { name: 'Comprehensive Metabolic Panel (CMP)', value: '+8.2%', icon: PurpleCheck }
];

// Sleep tracker data
const sleep_data = [
  { day: 'Sunday', hours: 2 },
  { day: 'Monday', hours: 2 },
  { day: 'Tuesday', hours: 3 },
  { day: 'Wednesday', hours: 4 },
  { day: 'Thursday', hours: 5 },
  { day: 'Friday', hours: 4 },
  { day: 'Saturday', hours: 3 }
];

// Blood pressure tracker data
const blood_pressure_data = [
  { day: 'Sunday', reading: 2 },
  { day: 'Monday', reading: 3 },
  { day: 'Tuesday', reading: 4 },
  { day: 'Wednesday', reading: 3 },
  { day: 'Thursday', reading: 5 },
  { day: 'Friday', reading: 6 },
  { day: 'Saturday', reading: 5 }
];

// Top 5 activities data
const activities_data = [
  { name: 'Walking', points: '1,200pts', percentage: '+8.2%', color: '#47AEA9', value: 35 },
  { name: 'Running', points: '800pts', percentage: '+7%', color: '#91BF2C', value: 20 },
  { name: 'Stairs', points: '645pts', percentage: '+2.5%', color: '#A1D431', value: 15 },
  { name: 'Cycling', points: '590pts', percentage: '-6.5%', color: '#E2F2BF', value: 15 },
  { name: 'Swimming', points: '342pts', percentage: '+1.7%', color: '#F1F9E0', value: 15 }
];

const teal_colors = ["#47AEA9", "#91BF2C", "#A1D431", "#E2F2BF", "#F1F9E0"];

// Chart configuration constants
const chart_config = {
  yAxisDomain: [0, 8],
  yAxisTicks: [0, 2, 4, 6, 8],
  barSize: 50,
  margins: { top: 20, right: 10, left: -20, bottom: 30 }
};

export function HealthStats() {

  return (
    <div className="w-full px-2 sm:px-4 lg:px-6">
      {/* Top Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mb-6">
        {/* Average Heart Rate */}
        <div className="bg-white p-3 md:p-4 shadow-md border border-border rounded-lg">
          <div className="text-sm md:text-base text-coolGray60 mb-2">Average Heart Rate</div>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
            <div className="flex items-baseline text-paragraphContent font-bold">
              <span className="text-xl md:text-2xl">78</span>
              <span className="text-sm md:text-base ml-1">bpm</span>
            </div>
            <div className="text-xs md:text-sm px-2 md:px-3 py-1 bg-landingBackground rounded-full self-start">Today</div>
          </div>
        </div>

        {/* Daily Activity */}
        <div className="bg-white p-3 md:p-4 shadow-md border border-border rounded-lg">
          <div className="text-sm md:text-base text-coolGray60 mb-2">Daily Activity</div>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
            <div className="flex items-baseline text-paragraphContent font-bold">
              <span className="text-xl md:text-2xl">4,500</span>
              <span className="text-sm md:text-base ml-1">steps</span>
            </div>
            <div className="text-xs md:text-sm px-2 md:px-3 py-1 bg-coolGray60 text-white rounded-full self-start">
              <span>-1.2%</span>
            </div>
          </div>
        </div>

        {/* Recovery Score */}
        <div className="bg-white p-3 md:p-4 shadow-md border border-border rounded-lg">
          <div className="text-sm md:text-base text-coolGray60 mb-2">Recovery Score</div>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
            <div className="flex items-baseline text-paragraphContent font-bold">
              <span className="text-xl md:text-2xl">180/230</span>
            </div>
            <div className="text-xs md:text-sm px-2 md:px-3 py-1 bg-landingBackground rounded-full self-start">Today</div>
          </div>
        </div>

        {/* Pain Screening */}
        <div className="bg-white p-3 md:p-4 shadow-md border border-border rounded-lg">
          <div className="text-sm md:text-base text-coolGray60 mb-2">Pain Screening</div>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
            <div className="flex items-baseline text-paragraphContent font-bold">
              <span className="text-xl md:text-2xl">8/10</span>
            </div>
            <div className="text-xs md:text-sm px-2 md:px-3 py-1 bg-landingBackground rounded-full self-start">Today</div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-6">
        {/* Sleep Tracker */}
        <div className="bg-white p-4 md:p-5 shadow-md border border-border rounded-lg">
          <h2 className="text-lg md:text-xl font-bold mb-4">My Daily Sleep Tracker</h2>
          <div className="h-[250px] md:h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={sleep_data}
                margin={chart_config.margins}
                barSize={chart_config.barSize}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" vertical={true} horizontal={true} />
                <XAxis
                  dataKey="day"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 14, fill: '#6B7280' }}
                  dy={10}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  domain={chart_config.yAxisDomain}
                  ticks={chart_config.yAxisTicks}
                  tick={{ fontSize: 14, fill: '#6B7280' }}
                />
                <Tooltip
                  cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                  contentStyle={{ borderRadius: '8px', border: 'none', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}
                  formatter={(value) => [`${value} hours`, 'Sleep']}
                />
                <Bar
                  dataKey="hours"
                  name="Daily Hours of Sleep"
                  fill="#47AEA9"
                  radius={[0, 0, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-primary mr-2"></div>
              <span className="text-xs text-gray-600">Daily Hours of Sleep</span>
            </div>
          </div>
        </div>

        {/* Blood Pressure Tracker */}
        <div className="bg-white p-4 md:p-5 shadow-md border border-border rounded-lg">
          <h2 className="text-lg md:text-xl font-bold mb-4">My Weekly Blood Pressure Tracker</h2>
          <div className="h-[250px] md:h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={blood_pressure_data}
                margin={chart_config.margins}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" vertical={true} horizontal={true} />
                <XAxis
                  dataKey="day"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 14, fill: '#6B7280' }}
                  dy={10}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  domain={chart_config.yAxisDomain}
                  ticks={chart_config.yAxisTicks}
                  tick={{ fontSize: 14, fill: '#6B7280' }}
                />
                <Tooltip
                  cursor={{ stroke: '#38B2AC', strokeWidth: 1 }}
                  contentStyle={{ borderRadius: '8px', border: 'none', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}
                  formatter={(value) => [`${value}`, 'Blood Pressure']}
                />
                <Line
                  type="linear"
                  dataKey="reading"
                  stroke="#38B2AC"
                  strokeWidth={2}
                  dot={{ r: 4, fill: "#47AEA9", strokeWidth: 0 }}
                  activeDot={{ r: 6, fill: "#47AEA9", strokeWidth: 0 }}
                  connectNulls={true}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center mt-2">
            <div className="flex items-center">
              <div className="w-2 h-1 bg-[#38B2AC]"></div>
              <div className="w-3 h-3 bg-[#38B2AC] rounded-full"></div>
              <div className="w-2 h-1 bg-[#38B2AC] mr-2"></div>
              <span className="text-xs text-gray-600">Daily Blood Pressure Reading</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Stats Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* Health Stats List */}
        <div className="bg-white p-4 md:p-5 shadow-md border border-border rounded-lg">
          <h2 className="text-lg md:text-xl font-bold mb-4">My Health Stats</h2>
          <div className="space-y-1">
            {health_stats_data.map((stat, index) => (
              <div key={index} className="flex items-center justify-between py-1">
                <div className="flex items-center min-w-0 flex-1">
                  <div className="border border-border rounded-full p-1 bg-landingBackground mr-3 flex-shrink-0">
                    <stat.icon />
                  </div>
                  <span className="text-sm text-gray-700 truncate">{stat.name}</span>
                </div>
                <div className='px-2 md:px-3 py-1 bg-landingBackground rounded-full flex-shrink-0'>
                  <span className="text-xs md:text-sm text-paragraphContent">{stat.value}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top 5 Activities */}
        <div className="bg-white p-4 md:p-5 shadow-md border border-border rounded-lg">
          <h2 className="text-lg md:text-xl font-bold mb-4">My Top 5 Activities This Week</h2>
          <div className="w-full">
            <DonutChart
              title=""
              colors={teal_colors}
              data={activities_data}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
