import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export function Header() {
  return (
    <header className="w-full py-4 md:px-20 max-md:px-5 flex justify-between items-center border-b mx-auto ">
      <div className="flex justify-between w-full max-md:justify-center container">
        <Link href="/" className="flex items-center">
          <Image
            src="/logo.svg"
            alt="Peakality Logo"
            width={209}
            height={25}
          />
        </Link>
        <div className="flex gap-4 max-md:hidden">
          <Link href="/signup">
            <Button
              variant="primary"
              className="text-body-large text-lightBlue"
            >
              Sign Up
            </Button>
          </Link>
          <Link href="/login">
            <Button variant="outlined" className="text-body-large text-primary">
              Login
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
}
