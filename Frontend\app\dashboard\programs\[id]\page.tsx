"use client"

import { ProgramDetail } from '@/components/programs/ProgramDetail'
import { useParams } from 'next/navigation'
import { useEffect, useState } from 'react'

// Sample program data
const programsData = [
  {
    id: 1,
    title: 'Wheel of Life Challenge',
    image: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    challenges: 30,
    duration: 30,
    reward: 300,
    description: 'Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui. Fusce vulputate tincidunt vulputat lectus quam felis nisi augue egestas. Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui. Fusce vulputate tincidunt vulputat lectus quam felis nisi augue egestas.',
    instructor: {
      name: '<PERSON>',
      courses: 7
    }
  },
  {
    id: 2,
    title: '33 Questions Challenge',
    image: 'https://images.unsplash.com/photo-1501504905252-473c47e087f8?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1074&q=80',
    challenges: 30,
    duration: 30,
    reward: 300,
    description: 'Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui. Fusce vulputate tincidunt vulputat lectus quam felis nisi augue egestas. Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui.',
    instructor: {
      name: 'Carol Potter',
      courses: 7
    }
  },
  {
    id: 3,
    title: 'Decision Destination Challenge',
    image: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    challenges: 30,
    duration: 30,
    reward: 300,
    description: 'Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui. Fusce vulputate tincidunt vulputat lectus quam felis nisi augue egestas.',
    instructor: {
      name: 'Carol Potter',
      courses: 7
    }
  }
]

export default function ProgramDetailPage() {
  const params = useParams()
  const [program, setProgram] = useState<any>(null)

  useEffect(() => {
    if (params.id) {
      const programId = parseInt(params.id as string)
      const foundProgram = programsData.find(p => p.id === programId)
      if (foundProgram) {
        setProgram(foundProgram)
      }
    }
  }, [params.id])

  if (!program) {
    return <div className="p-8 text-center">Loading program details...</div>
  }

  return (
    <ProgramDetail
      id={program.id}
      title={program.title}
      image={program.image}
      challenges={program.challenges}
      duration={program.duration}
      reward={program.reward}
      description={program.description}
      instructor={program.instructor}
    />
  )
}